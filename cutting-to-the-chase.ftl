<w:tbl xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:tblPr>
    <w:tblStyle w:val="TableGrid"/>
    <w:tblW w:w="${styling.table.width}" w:type="${styling.table.widthType}" />
    <w:tblBorders>
      <#-- Only render borders if not 'nil' -->
      <#if styling.table.borders.top?? && styling.table.borders.top != "nil">
        <w:top w:val="${styling.table.borders.top!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.left?? && styling.table.borders.left != "nil">
        <w:left w:val="${styling.table.borders.left!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.bottom?? && styling.table.borders.bottom != "nil">
        <w:bottom w:val="${styling.table.borders.bottom!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.right?? && styling.table.borders.right != "nil">
        <w:right w:val="${styling.table.borders.right!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.insideH?? && styling.table.borders.insideH != "nil">
        <w:insideH w:val="${styling.table.borders.insideH!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
      <#if styling.table.borders.insideV?? && styling.table.borders.insideV != "nil">
        <w:insideV w:val="${styling.table.borders.insideV!''}" w:sz="4" w:space="0" w:color="000000"/>
      </#if>
    </w:tblBorders>
    <w:tblCellSpacing w:w="0" w:type="dxa" />
  </w:tblPr>
  <w:tblGrid>
    <#list carriers as carrier>
      <w:gridCol w:w="${styling.columns.carrier.width}" w:type="dxa"/>
    </#list>
  </w:tblGrid>
  
  <!-- Ranking Row without Background -->
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="300" w:hRule="atLeast" />
    </w:trPr>
    <#list carriers as carrier>
      <#assign rankIndex = carrier?index + 1>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="${styling.columns.carrier.width}" w:type="dxa" />
          <w:vAlign w:val="center" />
          <w:tcMar>
            <w:top w:w="40" w:type="dxa" />
            <w:bottom w:w="40" w:type="dxa" />
            <w:left w:w="40" w:type="dxa" />
            <w:right w:w="40" w:type="dxa" />
          </w:tcMar>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="center" />
            <w:spacing w:before="40" w:after="40" />
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
              <w:color w:val="${styling.rows.header.cell.backgroundColor}" />
              <w:sz w:val="36" />
            </w:rPr>
            <w:t>${rankIndex}</w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#list>
  </w:tr>
  
  <!-- Carrier Names Row -->
  <w:tr>
    <w:trPr>
      <w:trHeight w:val="300" w:hRule="atLeast" />
    </w:trPr>
    <#list carriers as carrier>
      <w:tc>
        <w:tcPr>
          <w:tcW w:w="${styling.columns.carrier.width}" w:type="dxa" />
          <w:shd w:val="clear" w:color="auto" w:fill="${styling.rows.header.cell.backgroundColor}" />
          <w:vAlign w:val="center" />
          <w:tcMar>
            <w:top w:w="65" w:type="dxa" />
            <w:bottom w:w="65" w:type="dxa" />
            <w:left w:w="65" w:type="dxa" />
            <w:right w:w="65" w:type="dxa" />
          </w:tcMar>
        </w:tcPr>
        <w:p>
          <w:pPr>
            <w:jc w:val="center" />
            <w:spacing w:before="36" w:after="36" />
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
              <w:color w:val="FFFFFF" />
              <w:sz w:val="36" />
            </w:rPr>
            <w:t>${carrier}</w:t>
          </w:r>
        </w:p>
      </w:tc>
    </#list>
  </w:tr>
  
  <!-- Total Monthly Premiums Row -->
  <#list sections as section>
    <#if section.id == "calculations">
      <#list section.benefits as benefit>
        <#if benefit.key == "totalMonthlyPremiums">
          <w:tr>
            <w:trPr>
              <w:trHeight w:val="200" w:hRule="atLeast" />
            </w:trPr>
            <#list carriers as carrier>
              <w:tc>
                <w:tcPr>
                  <w:tcW w:w="${styling.columns.carrier.width}" w:type="dxa" />
                  <w:vAlign w:val="center" />
                  <w:tcMar>
                    <w:top w:w="40" w:type="dxa" />
                    <w:bottom w:w="40" w:type="dxa" />
                    <w:left w:w="40" w:type="dxa" />
                    <w:right w:w="40" w:type="dxa" />
                  </w:tcMar>
                </w:tcPr>
                <w:p>
                  <w:pPr>
                    <w:jc w:val="center" />
                    <w:spacing w:before="80" w:after="80" />
                  </w:pPr>
                  <w:r>
                    <w:rPr>
                      <w:b/>
                      <w:color w:val="${styling.rows.subheader.cell.color}" />
                      <w:sz w:val="36" />
                    </w:rPr>
                    <w:t>
                      <#if benefit.values[carrier]??>${benefit.values[carrier]}<#else>-</#if>
                    </w:t>
                  </w:r>
                </w:p>
              </w:tc>
            </#list>
          </w:tr>
        </#if>
      </#list>
    </#if>
  </#list>
  
  <!-- Percentage Difference Row (Last Row) -->
  <#list sections as section>
    <#if section.id == "calculations">
      <#list section.benefits as benefit>
        <#if benefit.key == "differencePercentage">
          <w:tr>
            <w:trPr>
              <w:trHeight w:val="600" w:hRule="atLeast" />
            </w:trPr>
            <#list carriers as carrier>
              <#assign carrierIndex = carrier?index + 1>
              <w:tc>
                <w:tcPr>
                  <w:tcW w:w="${styling.columns.carrier.width}" w:type="dxa" />
                  <w:vAlign w:val="center" />
                  <w:tcMar>
                    <w:top w:w="40" w:type="dxa" />
                    <w:bottom w:w="40" w:type="dxa" />
                    <w:left w:w="40" w:type="dxa" />
                    <w:right w:w="40" w:type="dxa" />
                  </w:tcMar>
                </w:tcPr>
                <w:p>
                  <w:pPr>
                    <w:jc w:val="center" />
                    <w:spacing w:before="120" w:after="120" />
                  </w:pPr>
                  <w:r>
                    <w:rPr>
                      <w:color w:val="FFFFFF" />
                      <w:sz w:val="36" />
                    </w:rPr>
                    <w:t>
                      <#if carrierIndex == 1>
                        <!-- No percentage for rank 1 (winner) -->
                      <#else>
                        <#if benefit.values[carrier]??>${benefit.values[carrier]}<#else>-</#if>
                      </#if>
                    </w:t>
                  </w:r>
                </w:p>
              </w:tc>
            </#list>
          </w:tr>
        </#if>
      </#list>
    </#if>
  </#list>
</w:tbl>