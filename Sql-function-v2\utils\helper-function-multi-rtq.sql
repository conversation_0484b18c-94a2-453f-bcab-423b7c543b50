<sql splitStatements="false">
            <![CDATA[
 CREATE OR REPLACE FUNCTION sandf.fn_get_resolved_employee_class(
        plan_uuid_param TEXT
    )
    RETURNS TEXT
    LANGUAGE plpgsql
    AS $$
    DECLARE
        result_name TEXT;
    BEGIN
        SELECT employee_class_name INTO result_name
        FROM sandf.fn_resolve_employee_class_global(plan_uuid_param)
        LIMIT 1;

        RETURN result_name;
    END;
    $$;
        ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
CREATE OR REPLACE FUNCTION sandf.fn_resolve_employee_class_global(
    plan_uuid_param TEXT
)
RETURNS TABLE(
    employee_class_name TEXT,
    employee_class_id INTEGER,
    employee_class_count INTEGER,
    all_employee_classes TEXT[],
    is_rtq_only BOOLEAN,
    resolution_strategy TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    v_plan_id INTEGER;
    detected_employee_class_count INTEGER;
    detected_employee_classes TEXT[];
    resolved_employee_class_name TEXT;
    resolved_employee_class_id INTEGER;
    is_single_rtq BOOLEAN := FALSE;
    strategy_used TEXT;
BEGIN
    -- Step 1: Get plan_id
    SELECT p.plan_id INTO v_plan_id
    FROM sandf.plan p
    WHERE p.plan_uuid = plan_uuid_param::uuid
    ORDER BY p.plan_id ASC
    LIMIT 1;

    IF v_plan_id IS NULL THEN
        RAISE EXCEPTION 'Plan not found for UUID: %', plan_uuid_param;
    END IF;

    -- Step 2: Detect all employee classes for this plan
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO detected_employee_class_count, detected_employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;

    -- Step 3: Apply resolution logic
    IF detected_employee_class_count = 1 AND detected_employee_classes[1] = 'RTQ' THEN
        -- Single RTQ employee class - use RTQ
        resolved_employee_class_name := 'RTQ';
        is_single_rtq := TRUE;
        strategy_used := 'SINGLE_RTQ';

        RAISE NOTICE 'Employee class resolution: Using single RTQ class';

    ELSE
        -- Multiple employee classes or non-RTQ - use first employee class (multi-class approach)
        resolved_employee_class_name := detected_employee_classes[1];
        is_single_rtq := FALSE;
        strategy_used := 'MULTI_CLASS_FIRST_OBJECT';

        RAISE NOTICE 'Employee class resolution: Using first employee class (%) from % total classes',
                     resolved_employee_class_name, detected_employee_class_count;
    END IF;

    -- Step 4: Get the employee_class_id for the resolved employee class
    SELECT ec.employee_class_id INTO resolved_employee_class_id
    FROM sandf.employee_class ec
    WHERE ec.plan_id = v_plan_id
    AND ec.name = resolved_employee_class_name
    ORDER BY ec.employee_class_id ASC
    LIMIT 1;

    -- Step 5: Return the resolved information
    RETURN QUERY SELECT
        resolved_employee_class_name,
        resolved_employee_class_id,
        detected_employee_class_count,
        detected_employee_classes,
        is_single_rtq,
        strategy_used;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error in global employee class resolver: % (SQLSTATE: %)', SQLERRM, SQLSTATE;
END;
$$;
        ]]>
        </sql>

           <sql splitStatements="false">
            <![CDATA[
            CREATE OR REPLACE FUNCTION fn_format_currency_with_symbol_java_style(
                amount NUMERIC
            )
            RETURNS TEXT
            LANGUAGE plpgsql
            AS $$
            BEGIN
                    IF
            amount IS NULL THEN
                        RETURN '$0.00';
            END IF;

                    IF
            amount = 0 THEN
                        RETURN '$0';
            END IF;

            RETURN '$' || to_char(round(amount, 2), 'FM999,999,990.00');
            END;
            $$;
            ]]>
        </sql>

        <sql splitStatements="false">
            <![CDATA[
            CREATE OR REPLACE FUNCTION fn_format_percentage_java_style(
                percentage NUMERIC
            )
            RETURNS TEXT
            LANGUAGE plpgsql
            AS $$
            BEGIN
                    IF
            percentage IS NULL THEN
                        RETURN '0.00%';
            END IF;

                    IF
            percentage = 0 THEN
                        RETURN '0%';
            END IF;

            RETURN to_char(round(percentage, 2), 'FM999,999,990.00') || '%';
            END;
            $$;
            ]]>
        </sql>    
     <sql splitStatements="false">
            <![CDATA[
             CREATE OR REPLACE FUNCTION sandf.get_user_preference_order(
    user_id_param TEXT,
    plan_uuid_param TEXT,
    quote_id_param BIGINT DEFAULT NULL,
    quote_uuid_param UUID DEFAULT NULL,
    default_order_param INTEGER DEFAULT 0
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    user_preferences JSONB;
    column_ordering JSONB;
    plan_column_ordering JSONB;
    result_order INTEGER;
    has_custom_ordering BOOLEAN := FALSE;
    alphabetical_index INTEGER;
    quote_type TEXT;
    total_monthly_premiums DECIMAL;
    plan_status TEXT;
    incomplete_quotes_count INTEGER;
BEGIN
    -- Determine plan status by checking if all quotes are complete
    -- A quote is complete if status is INFERENCE_COMPLETE, INFERENCE_FAILED, OCR_FAILED, TIMED_OUT, or ANALYSIS_FAILED
    SELECT COUNT(*)
    INTO incomplete_quotes_count
    FROM sandf.quote q
    WHERE q.plan_id = (SELECT plan_id FROM sandf.plan WHERE plan_uuid = plan_uuid_param::uuid)
    AND q.status NOT IN ('INFERENCE_COMPLETE', 'INFERENCE_FAILED', 'OCR_FAILED', 'TIMED_OUT', 'ANALYSIS_FAILED');

    plan_status := CASE WHEN incomplete_quotes_count = 0 THEN 'COMPLETE' ELSE 'IN_PROGRESS' END;

    -- Get quote information for sorting logic
    SELECT q.quote_type,
           COALESCE((ecq.quote_details -> 'benefitPremiums' ->> 'totalMonthlyPremiums')::DECIMAL, 0)
    INTO quote_type, total_monthly_premiums
    FROM sandf.quote q
    LEFT JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    LEFT JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE (quote_id_param IS NOT NULL AND q.quote_id = quote_id_param)
       OR (quote_uuid_param IS NOT NULL AND q.quote_uuid = quote_uuid_param)
    LIMIT 1;

    -- Get user preferences for column ordering
    SELECT preferences INTO user_preferences
    FROM sandf.users
    WHERE users_uuid = user_id_param::uuid;

    -- Extract column ordering from user preferences
    IF user_preferences IS NOT NULL THEN
        column_ordering := user_preferences -> 'columnOrdering';
        IF column_ordering IS NOT NULL THEN
            plan_column_ordering := column_ordering -> plan_uuid_param;
            -- Check if user has actually made any custom ordering changes for this plan
            IF plan_column_ordering IS NOT NULL AND jsonb_typeof(plan_column_ordering) = 'object' THEN
                -- Check if there are any entries in the plan_column_ordering
                IF EXISTS (SELECT 1 FROM jsonb_object_keys(plan_column_ordering) LIMIT 1) THEN
                    has_custom_ordering := TRUE;
                END IF;
            END IF;
        END IF;
    END IF;

    -- If user has custom ordering, use it first
    IF has_custom_ordering THEN
        -- First try quote_id as text (if provided)
        IF quote_id_param IS NOT NULL AND plan_column_ordering ? quote_id_param::text THEN
            result_order := (plan_column_ordering ->> quote_id_param::text)::integer;
        -- Then try quote_uuid (if provided)
        ELSIF quote_uuid_param IS NOT NULL AND plan_column_ordering ? quote_uuid_param::text THEN
            result_order := (plan_column_ordering ->> quote_uuid_param::text)::integer;
        -- If quote_id provided but not found, try to find quote_uuid from database
        ELSIF quote_id_param IS NOT NULL AND EXISTS (
            SELECT 1 FROM sandf.quote q
            WHERE q.quote_id = quote_id_param
            AND q.quote_uuid IS NOT NULL
            AND plan_column_ordering ? q.quote_uuid::text
        ) THEN
            SELECT (plan_column_ordering ->> q.quote_uuid::text)::integer
            INTO result_order
            FROM sandf.quote q
            WHERE q.quote_id = quote_id_param;
        ELSE
            -- If this specific quote is not in user's custom ordering, fall back to backend logic
            result_order := sandf.get_backend_display_order(plan_uuid_param, quote_id_param, quote_uuid_param, plan_status, quote_type, total_monthly_premiums, default_order_param);
        END IF;
    ELSE
        -- No custom ordering, use backend logic
        result_order := sandf.get_backend_display_order(plan_uuid_param, quote_id_param, quote_uuid_param, plan_status, quote_type, total_monthly_premiums, default_order_param);
    END IF;

    RETURN result_order;
END;
$$;
        ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
                    CREATE OR REPLACE FUNCTION sandf.get_backend_display_order(
    plan_uuid_param TEXT,
    quote_id_param BIGINT DEFAULT NULL,
    quote_uuid_param UUID DEFAULT NULL,
    plan_status TEXT DEFAULT NULL,
    quote_type TEXT DEFAULT NULL,
    total_monthly_premiums DECIMAL DEFAULT 0,
    default_order_param INTEGER DEFAULT 0
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    result_order INTEGER;
    alphabetical_index INTEGER;
    price_index INTEGER;
    current_quote_priority INTEGER;
BEGIN
    -- Step 1: Calculate current quote priority (CURRENT quotes get priority 0, others get 1)
    current_quote_priority := CASE WHEN quote_type = 'CURRENT' THEN 0 ELSE 1 END;

    -- Step 2: Calculate alphabetical index based on carrier name
    SELECT COALESCE(
        (SELECT COUNT(*)
         FROM sandf.quote q2
         JOIN sandf.carrier c2 ON q2.carrier_id = c2.carrier_id
         WHERE q2.plan_id = (SELECT plan_id FROM sandf.plan WHERE plan_uuid = plan_uuid_param::uuid)
         AND c2.description < c.description
         AND q2.quote_uuid IS NOT NULL), 0)
    INTO alphabetical_index
    FROM sandf.quote q
    JOIN sandf.carrier c ON q.carrier_id = c.carrier_id
    WHERE (quote_id_param IS NOT NULL AND q.quote_id = quote_id_param)
       OR (quote_uuid_param IS NOT NULL AND q.quote_uuid = quote_uuid_param);

    -- Step 3: Calculate price-based index
    SELECT COALESCE(
        (SELECT COUNT(*)
         FROM sandf.quote q2
         JOIN sandf.employee_class_quote ecq2 ON q2.quote_id = ecq2.quote_id
         JOIN sandf.employee_class ec2 ON ecq2.employee_class_id = ec2.employee_class_id
         WHERE q2.plan_id = (SELECT plan_id FROM sandf.plan WHERE plan_uuid = plan_uuid_param::uuid)
         AND COALESCE((ecq2.quote_details -> 'benefitPremiums' ->> 'totalMonthlyPremiums')::DECIMAL, 0) < total_monthly_premiums
         AND q2.quote_uuid IS NOT NULL), 0)
    INTO price_index
    FROM sandf.quote q
    WHERE (quote_id_param IS NOT NULL AND q.quote_id = quote_id_param)
       OR (quote_uuid_param IS NOT NULL AND q.quote_uuid = quote_uuid_param);

    -- Step 4: Determine final order based on backend logic (default to LOWEST_PRICE behavior)
    -- Default fallback logic: Current quote first, then alphabetical if IN_PROGRESS, then by price
    IF plan_status = 'IN_PROGRESS' THEN
        result_order := (current_quote_priority * 1000000) + (alphabetical_index * 1000) + price_index;
    ELSE
        result_order := (current_quote_priority * 1000000) + price_index;
    END IF;

    RETURN COALESCE(result_order, default_order_param);
END;
$$;
                    ]]>
        </sql>

  <sql splitStatements="false">
            <![CDATA[
           CREATE OR REPLACE FUNCTION sandf.safe_premium_value(premium_text TEXT, fallback NUMERIC DEFAULT 0)
RETURNS NUMERIC
LANGUAGE plpgsql
AS $$
DECLARE
    premium_val NUMERIC;
BEGIN
    IF premium_text IS NULL OR trim(premium_text) = '' THEN
        RETURN fallback;
    END IF;
    BEGIN
        premium_val := premium_text::NUMERIC;
        RETURN premium_val;
    EXCEPTION WHEN invalid_text_representation THEN
        RETURN fallback;
    END;
END;
$$;
        ]]>
        </sql>
        <sql splitStatements="false">
            <![CDATA[
          CREATE OR REPLACE FUNCTION sandf.safe_parse_numeric(val TEXT)
RETURNS NUMERIC
LANGUAGE plpgsql
AS $$
BEGIN
    IF val IS NULL OR trim(val) = '' THEN
        RETURN 0;
    END IF;
    BEGIN
        RETURN val::NUMERIC;
    EXCEPTION WHEN invalid_text_representation THEN
        RETURN 0;
    END;
END;
$$;
        ]]>
        </sql>
   <sql splitStatements="false">
            <![CDATA[
         CREATE OR REPLACE FUNCTION format_currency(value JSONB)
        RETURNS TEXT
        LANGUAGE plpgsql
        AS $$
        BEGIN
            -- Handle null or non-numeric values
            IF value IS NULL OR jsonb_typeof(value) = 'null' THEN
                RETURN '$0.00';
            END IF;

            -- Handle text values like '-'
            IF jsonb_typeof(value) = 'string' AND (value #>> '{}') = '-' THEN
                RETURN '$0.00';
            END IF;

            -- Handle numeric values with proper rounding and comma formatting
            IF jsonb_typeof(value) = 'number' THEN
                RETURN '$' || to_char(ROUND((value #>> '{}')::NUMERIC, 2), 'FM999,999,990.00');
            END IF;

            -- Try to convert string to numeric with proper rounding and comma formatting
            BEGIN
                RETURN '$' || to_char(ROUND((value #>> '{}')::NUMERIC, 2), 'FM999,999,990.00');
            EXCEPTION WHEN OTHERS THEN
                RETURN '$0.00';
            END;
        END;
        $$;
        ]]>
        </sql>

        <sql splitStatements="false">
            <![CDATA[
         CREATE OR REPLACE FUNCTION sandf.format_currency(value JSONB)
        RETURNS TEXT
        LANGUAGE plpgsql
        AS $$
        BEGIN
            -- Handle null or non-numeric values
            IF value IS NULL OR jsonb_typeof(value) = 'null' THEN
                RETURN '$0.00';
            END IF;

            -- Handle text values like '-'
            IF jsonb_typeof(value) = 'string' AND (value #>> '{}') = '-' THEN
                RETURN '$0.00';
            END IF;

            -- Handle numeric values with proper rounding and comma formatting
            IF jsonb_typeof(value) = 'number' THEN
                RETURN '$' || to_char(ROUND((value #>> '{}')::NUMERIC, 2), 'FM999,999,990.00');
            END IF;

            -- Try to convert string to numeric with proper rounding and comma formatting
            BEGIN
                RETURN '$' || to_char(ROUND((value #>> '{}')::NUMERIC, 2), 'FM999,999,990.00');
            EXCEPTION WHEN OTHERS THEN
                RETURN '$0.00';
            END;
        END;
        $$;
        ]]>
        </sql>

        