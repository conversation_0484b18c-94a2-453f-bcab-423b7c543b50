# Plan Design Group Function Updates

## Overview
Updated the `fn_get_plan_design_report_group_class` function to use the same configuration-based data filtering and ordering as the multi-class function.

## Function Renamed
- **Before**: `fn_get_plan_design_report_multi_class` (conflicted with multi-class function)
- **After**: `fn_get_plan_design_report_group_class` (unique name for group functionality)

## Key Changes Applied

### 1. Configuration-Based Filtering
- **Added**: Same `config_plan_details` structure with explicit order numbers (1-9)
- **Filtering**: Only processes sections and fields defined in the configuration
- **Field Mapping**: Uses `displayItems` from config for proper field names and display
- **Data Validation**: Filters out fields not mentioned in the configuration

### 2. Explicit Section Ordering
- **Before**: Used `ui_field.display_order` for section ordering
- **After**: Uses explicit `order` numbers from `config_plan_details`
- **Order**: lifeInsurance(1), ADAD(2), dependentLife(3), longTermDisability(4), shortTermDisability(5), criticalIllness(6), employeeAssistance(7), extendedHealth(8), dental(9)
- **Consistent**: Same ordering logic as the multi-class function

### 3. lifeInsuranceADAD Handling
- **Split Processing**: Properly splits `lifeInsuranceADAD` into separate `lifeInsurance` and `ADAD` sections
- **Configuration**: Each section uses its own displayItems configuration
- **Class Mapping**: Maintains proper section naming in the class grouping structure

### 4. Enhanced Field Processing
- **Both Paths**: Updated both array-based and object-based field processing paths
- **Config Validation**: Only processes fields that exist in `displayItems`
- **Display Names**: Uses configuration-defined display names instead of database lookups
- **Carrier Logic**: Maintains "Current" carrier naming for first carrier

### 5. Class Grouping Integration
- **Preserved**: All existing class grouping and legend functionality
- **Enhanced**: Class sections now use configuration-based section names and ordering
- **Mapping**: Updated `class_sections_map` to use mapped section keys from configuration

## Configuration Structure Used

Same structure as multi-class function:

```json
{
  "planDetails": {
    "lifeInsurance": {
      "order": 1,
      "lifeInsurance": "Life Insurance",
      "displayItems": {
        "maximumLife": "Maximum LI",
        "ageReduction": "Age Reduction",
        "coverageLife": "Coverage LI",
        "terminationAgeLife": "Termination Age LI"
      }
    },
    "ADAD": {
      "order": 2,
      "ADAD": "Accidental Death & Dismembermenet",
      "displayItems": {
        "maximumADAD": "Maximum AD&D",
        "terminationAgeADAD": "Termination Age ADAD"
      }
    }
    // ... other sections with orders 3-9
  }
}
```

## Benefits

1. **Consistency**: Same configuration logic as multi-class function
2. **Maintainable**: Changes to section names or field mappings only require config.json updates
3. **Flexible**: Easy to add/remove sections by modifying configuration
4. **Clean Output**: Only shows relevant data as defined in configuration
5. **Proper Grouping**: Maintains all class grouping functionality with enhanced ordering
6. **Data-Driven**: Section ordering based on explicit configuration numbers

## Usage

The function maintains the same interface but with a new name:
```sql
SELECT sandf.fn_get_plan_design_report_group_class(
    plan_uuid_param,
    user_id_param,
    includes_param,
    excludes_param
);
```

The output structure maintains the same class grouping format but with configuration-based section ordering and filtering.
