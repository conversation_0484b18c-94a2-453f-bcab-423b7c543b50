 <sql splitStatements="false">
            <![CDATA[
         CREATE OR REPLACE FUNCTION sandf.fn_get_no_of_employee_count(
    plan_uuid_param TEXT,
    includes_quotes_uuid TEXT[] DEFAULT NULL
)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    benefit_premiums_key TEXT := 'benefitPremiums';
    quote_record RECORD;
    benefit_premiums JSONB;
    extended_health_premium JSONB;
    total_ehs_volume INTEGER := 0;  -- Extended Health Single volume
    total_ehf_volume INTEGER := 0;  -- Extended Health Family volume
    total_employee_count INTEGER := 0;
    single_volume INTEGER;
    family_volume INTEGER;
    volume_text TEXT;
    employee_class_count INTEGER;
    processed_quotes TEXT[] := '{}';  -- Track processed quotes to avoid duplicates
    current_quote_key TEXT;
BEGIN
    RAISE NOTICE 'Starting employee count calculation for plan: %', plan_uuid_param;

    -- Check how many employee classes exist for this plan
    SELECT COUNT(DISTINCT ec.name)
    INTO employee_class_count
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb
    AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid));

    RAISE NOTICE 'Found % employee classes for this plan', employee_class_count;

    -- Loop through ALL employee classes and quotes for the plan
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description AS carrier_description,
               q.quote_id,
               q.quote_uuid,
               ec.name AS employee_class_name
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
          AND ecq.formatted_quote_details IS NOT NULL
          AND ecq.formatted_quote_details != '{}'::jsonb
          AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
        ORDER BY ec.name, c.description
    LOOP
        -- Create a unique key for this quote to avoid processing duplicates
        current_quote_key := quote_record.quote_uuid::text || '_' || quote_record.employee_class_name;

        -- Skip if we've already processed this quote
        IF current_quote_key = ANY(processed_quotes) THEN
            RAISE NOTICE 'Skipping already processed quote: % for class: %', quote_record.carrier_description, quote_record.employee_class_name;
            CONTINUE;
        END IF;

        -- Add to processed quotes
        processed_quotes := array_append(processed_quotes, current_quote_key);

        RAISE NOTICE 'Processing quote from carrier: % for employee class: %', quote_record.carrier_description, quote_record.employee_class_name;

        -- Extract benefit premiums
        benefit_premiums := quote_record.formatted_quote_details -> benefit_premiums_key;

        RAISE NOTICE 'Full formatted_quote_details for %: %', quote_record.employee_class_name, quote_record.formatted_quote_details;
        RAISE NOTICE 'Benefit premiums for %: %', quote_record.employee_class_name, benefit_premiums;

        IF benefit_premiums IS NOT NULL AND jsonb_typeof(benefit_premiums) = 'object' THEN
            -- Extract Extended Health Premium data
            extended_health_premium := benefit_premiums -> 'extendedHealthPremium';

            RAISE NOTICE 'Extended Health Premium raw data for %: %', quote_record.employee_class_name, extended_health_premium;

            IF extended_health_premium IS NOT NULL AND jsonb_typeof(extended_health_premium) = 'object' THEN
                RAISE NOTICE 'Extended Health Premium data found for %: %', quote_record.employee_class_name, extended_health_premium;

                -- Check if it has nested structure (single/couple/family) or direct structure
                IF extended_health_premium ? 'single' OR extended_health_premium ? 'couple' OR extended_health_premium ? 'family' THEN
                    RAISE NOTICE 'Found nested structure (single/couple/family) for %', quote_record.employee_class_name;

                    -- Extract Single volume (EHS)
                    IF extended_health_premium ? 'single' THEN
                        volume_text := extended_health_premium -> 'single' ->> 'volume';
                        RAISE NOTICE 'Single volume text for %: %', quote_record.employee_class_name, volume_text;
                        BEGIN
                            single_volume := CASE
                                WHEN volume_text IS NULL OR TRIM(volume_text) = '' THEN 0
                                ELSE COALESCE((regexp_replace(TRIM(volume_text), '[^0-9\.-]', '', 'g'))::INTEGER, 0)
                            END;
                            total_ehs_volume := total_ehs_volume + single_volume;
                            RAISE NOTICE 'Extended Health Single volume for % (class %): %', quote_record.carrier_description, quote_record.employee_class_name, single_volume;
                        EXCEPTION
                            WHEN OTHERS THEN
                                single_volume := 0;
                                RAISE NOTICE 'Error parsing single volume for %, defaulting to 0', quote_record.employee_class_name;
                        END;
                    END IF;

                    -- Extract Family volume (EHF)
                    IF extended_health_premium ? 'family' THEN
                        volume_text := extended_health_premium -> 'family' ->> 'volume';
                        RAISE NOTICE 'Family volume text for %: %', quote_record.employee_class_name, volume_text;
                        BEGIN
                            family_volume := CASE
                                WHEN volume_text IS NULL OR TRIM(volume_text) = '' THEN 0
                                ELSE COALESCE((regexp_replace(TRIM(volume_text), '[^0-9\.-]', '', 'g'))::INTEGER, 0)
                            END;
                            total_ehf_volume := total_ehf_volume + family_volume;
                            RAISE NOTICE 'Extended Health Family volume for % (class %): %', quote_record.carrier_description, quote_record.employee_class_name, family_volume;
                        EXCEPTION
                            WHEN OTHERS THEN
                                family_volume := 0;
                                RAISE NOTICE 'Error parsing family volume for %, defaulting to 0', quote_record.employee_class_name;
                        END;
                    END IF;
                ELSE
                    -- Handle direct structure (legacy format) - iterate through all keys
                    RAISE NOTICE 'Found direct structure (legacy format) for %', quote_record.employee_class_name;
                    DECLARE
                        key_name TEXT;
                        key_value JSONB;
                    BEGIN
                        FOR key_name IN SELECT jsonb_object_keys(extended_health_premium)
                        LOOP
                            key_value := extended_health_premium -> key_name;
                            RAISE NOTICE 'Processing EHC key % with value % for %', key_name, key_value, quote_record.employee_class_name;
                            BEGIN
                                volume_text := key_value ->> 'volume';
                                single_volume := CASE
                                    WHEN volume_text IS NULL OR TRIM(volume_text) = '' THEN 0
                                    ELSE COALESCE((regexp_replace(TRIM(volume_text), '[^0-9\.-]', '', 'g'))::INTEGER, 0)
                                END;

                                -- Add to appropriate total based on key name
                                IF key_name ILIKE '%single%' THEN
                                    total_ehs_volume := total_ehs_volume + single_volume;
                                    RAISE NOTICE 'Added % to EHS volume from key % for %', single_volume, key_name, quote_record.employee_class_name;
                                ELSIF key_name ILIKE '%family%' THEN
                                    total_ehf_volume := total_ehf_volume + single_volume;
                                    RAISE NOTICE 'Added % to EHF volume from key % for %', single_volume, key_name, quote_record.employee_class_name;
                                ELSE
                                    -- For other keys, add to total (could be general volume)
                                    total_ehs_volume := total_ehs_volume + single_volume;
                                    RAISE NOTICE 'Added % to EHS volume from general key % for %', single_volume, key_name, quote_record.employee_class_name;
                                END IF;
                            EXCEPTION
                                WHEN OTHERS THEN
                                    RAISE NOTICE 'Error parsing volume for key % in %, defaulting to 0', key_name, quote_record.employee_class_name;
                            END;
                        END LOOP;
                    END;
                END IF;
            ELSE
                RAISE NOTICE 'No Extended Health Premium data found in benefit premiums for class: %', quote_record.employee_class_name;
            END IF;
        ELSE
            RAISE NOTICE 'No benefit premiums found in quote details for class: %', quote_record.employee_class_name;
        END IF;
    END LOOP;

    -- Calculate total employee count (EHS + EHF volumes)
    total_employee_count := total_ehs_volume + total_ehf_volume;

    RAISE NOTICE 'Final calculation:';
    RAISE NOTICE 'Total EHS (Extended Health Single) volume: %', total_ehs_volume;
    RAISE NOTICE 'Total EHF (Extended Health Family) volume: %', total_ehf_volume;
    RAISE NOTICE 'Total Employee Count: %', total_employee_count;

    RETURN total_employee_count;
END;
            $$;

        ]]>
        </sql>