-- =====================================================
-- PLAN DESIGN HELPER FUNCTIONS
-- =====================================================
-- Common helper functions for plan design report generation
-- Extracted from plan-design-rtq.sql, plan-design-multi.sql, and plan-design-ungroup-multi.sql
-- to reduce code duplication and improve maintainability

-- =====================================================
-- 1. CONFIGURATION LOADING FUNCTIONS
-- =====================================================

/**
 * Load configuration from database
 * Returns the full config JSON from config.json_storage table
 */
CREATE OR REPLACE FUNCTION sandf.load_plan_design_config()
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    config_json JSONB;
BEGIN
    -- Load configuration from config.json_storage table
    SELECT json_data INTO config_json
    FROM config.json_storage
    WHERE properties = '{"config": "SURVEY_REPORT_CONFIG"}'
    LIMIT 1;

    -- Return empty object if no config found
    IF config_json IS NULL THEN
        config_json := '{}'::jsonb;
    END IF;

    RETURN config_json;
END;
$$;

/**
 * Extract planDetails configuration from loaded config
 * Returns the planDetails section with proper structure
 */
CREATE OR REPLACE FUNCTION sandf.get_plan_details_config(config_json JSONB)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    plan_details JSONB;
BEGIN
    -- Extract planDetails from config
    plan_details := config_json -> 'planDetails';

    -- Return empty object if planDetails not found
    IF plan_details IS NULL THEN
        plan_details := '{}'::jsonb;
    END IF;

    RETURN plan_details;
END;
$$;

/**
 * Get ordered section keys from plan details config
 * Returns array of section keys ordered by their 'order' property
 */
CREATE OR REPLACE FUNCTION sandf.get_ordered_section_keys(config_plan_details JSONB)
RETURNS TEXT[]
LANGUAGE plpgsql
AS $$
DECLARE
    config_section_keys TEXT[];
BEGIN
    -- Get the order of sections from config_plan_details keys (maintains order from config.json)
    SELECT array_agg(key ORDER BY ordinality) INTO config_section_keys
    FROM jsonb_each(config_plan_details) WITH ORDINALITY;

    -- Return empty array if no keys found
    IF config_section_keys IS NULL THEN
        config_section_keys := ARRAY[]::TEXT[];
    END IF;

    RETURN config_section_keys;
END;
$$;

-- =====================================================
-- 2. QUOTE PROCESSING AND CARRIER ORDERING FUNCTIONS
-- =====================================================

/**
 * Build carrier order map for quotes
 * Returns JSONB map with carrier names as keys and order info as values
 */
CREATE OR REPLACE FUNCTION sandf.build_carrier_order_map(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    employee_class_name TEXT DEFAULT 'RTQ'
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    quote_record RECORD;
    carrier_order_map JSONB := '{}'::jsonb;
    carrier_name TEXT;
    carrier_order INTEGER;
BEGIN
    -- Build carrier order map by processing all quotes
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = employee_class_name
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
    LOOP
        carrier_name := quote_record.carrier_description;

        -- Get carrier order using existing function
        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999  -- default order
        );

        -- Build carrier order map using carrier name as key
        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order,
                    'quote_id', quote_record.quote_id,
                    'quote_uuid', quote_record.quote_uuid
                )
            );
        END IF;
    END LOOP;

    RETURN carrier_order_map;
END;
$$;

/**
 * Get first carrier name from carrier order map
 * Returns the carrier name with the lowest order value
 */
CREATE OR REPLACE FUNCTION sandf.get_first_carrier_name(carrier_order_map JSONB)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    first_carrier_name TEXT;
    carrier_item TEXT;
BEGIN
    -- Get the first carrier name based on order
    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
        LIMIT 1
    LOOP
        first_carrier_name := carrier_item;
    END LOOP;

    RETURN first_carrier_name;
END;
$$;

/**
 * Build ordered carriers array with display names
 * Returns JSONB array of carrier names with "Current" for first carrier
 */
CREATE OR REPLACE FUNCTION sandf.build_ordered_carriers_array(
    carrier_order_map JSONB,
    first_carrier_name TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    display_carrier_name TEXT;
BEGIN
    -- Build ordered carriers array
    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
    LOOP
        -- Use "Current" for the first carrier, otherwise use actual name
        IF carrier_item = first_carrier_name THEN
            display_carrier_name := 'Current';
        ELSE
            display_carrier_name := carrier_item;
        END IF;

        ordered_carriers_array := ordered_carriers_array || jsonb_build_array(display_carrier_name);
    END LOOP;

    RETURN ordered_carriers_array;
END;
$$;

-- =====================================================
-- 3. VALIDATION AND UTILITY FUNCTIONS
-- =====================================================

/**
 * Get display carrier name (Current vs actual name)
 * Returns "Current" for first carrier, actual name for others
 */
CREATE OR REPLACE FUNCTION sandf.get_display_carrier_name(
    carrier_name TEXT,
    first_carrier_name TEXT
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
    -- Use "Current" for first carrier, otherwise use actual name
    IF carrier_name = first_carrier_name THEN
        RETURN 'Current';
    ELSE
        RETURN carrier_name;
    END IF;
END;
$$;

/**
 * Validate and clean carrier value
 * Returns cleaned value or '-' if invalid
 */
CREATE OR REPLACE FUNCTION sandf.validate_carrier_value(carrier_value TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
    IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
        RETURN '$0.00';
    ELSE
        -- Validate as numeric if appropriate
        BEGIN
            PERFORM sandf.safe_parse_numeric(carrier_value);
            RETURN carrier_value;
        EXCEPTION WHEN OTHERS THEN
            RETURN '$0.00';
        END;
    END IF;
END;
$$;

-- =====================================================
-- 4. SKIP CONDITION LOGIC FUNCTIONS
-- =====================================================

/**
 * Check if field should be skipped based on skip condition logic
 * Returns TRUE if field should be skipped, FALSE otherwise
 */
CREATE OR REPLACE FUNCTION sandf.should_skip_field(
    benefit_key TEXT,
    skip_fields TEXT[],
    coverage_life_values JSONB,
    field_values_to_check JSONB
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    should_skip_field BOOLEAN := FALSE;
    carrier_check TEXT;
    coverage_value TEXT;
    field_value TEXT;
BEGIN
    -- Check if this is one of the fields to be compared with coverageLife
    IF benefit_key = ANY(skip_fields) THEN
        should_skip_field := TRUE;

        -- Check if all carriers have the same value for this field AND coverageLife
        FOR carrier_check IN SELECT jsonb_object_keys(coverage_life_values)
        LOOP
            coverage_value := coverage_life_values ->> carrier_check;
            field_value := (field_values_to_check -> benefit_key) ->> carrier_check;

            -- If any carrier has different values, don't skip
            IF coverage_value != field_value THEN
                should_skip_field := FALSE;
                EXIT;
            END IF;
        END LOOP;
    END IF;

    RETURN should_skip_field;
END;
$$;

/**
 * Store coverage life values for skip condition checking
 * Updates the coverage_life_values JSONB with new carrier value
 */
CREATE OR REPLACE FUNCTION sandf.store_coverage_life_value(
    coverage_life_values JSONB,
    display_carrier_name TEXT,
    carrier_value TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN coverage_life_values || jsonb_build_object(display_carrier_name, carrier_value);
END;
$$;

/**
 * Store field values for skip condition checking
 * Updates the field_values_to_check JSONB with new field value
 */
CREATE OR REPLACE FUNCTION sandf.store_field_value_for_checking(
    field_values_to_check JSONB,
    benefit_key TEXT,
    display_carrier_name TEXT,
    carrier_value TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
BEGIN
    -- Initialize field key if not exists
    IF NOT field_values_to_check ? benefit_key THEN
        field_values_to_check := field_values_to_check || jsonb_build_object(benefit_key, '{}'::jsonb);
    END IF;

    -- Store the value
    RETURN jsonb_set(
        field_values_to_check,
        ARRAY[benefit_key, display_carrier_name],
        to_jsonb(carrier_value)
    );
END;
$$;

-- =====================================================
-- 5. SECTION AND BENEFIT PROCESSING FUNCTIONS
-- =====================================================

/**
 * Check if section should be processed based on configuration
 * Returns mapped section key and config info if valid, NULL otherwise
 */
CREATE OR REPLACE FUNCTION sandf.get_section_config_info(
    group_name TEXT,
    config_plan_details JSONB,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL,
    OUT mapped_section_key TEXT,
    OUT config_section_info JSONB
)
LANGUAGE plpgsql
AS $$
BEGIN
    mapped_section_key := NULL;
    config_section_info := NULL;

    -- Map lifeInsuranceADAD to both lifeInsurance and ADAD sections
    IF group_name = 'lifeInsuranceADAD' THEN
        -- Process as lifeInsurance first
        IF config_plan_details ? 'lifeInsurance' THEN
            mapped_section_key := 'lifeInsurance';
            config_section_info := config_plan_details -> 'lifeInsurance';
        END IF;
    ELSIF config_plan_details ? group_name THEN
        mapped_section_key := group_name;
        config_section_info := config_plan_details -> group_name;
    END IF;

    -- Check includes/excludes filters
    IF config_section_info IS NOT NULL AND
       (includes_param IS NULL OR group_name = ANY(includes_param)) AND
       (excludes_param IS NULL OR NOT (group_name = ANY(excludes_param))) THEN
        -- Return the values (already set above)
        RETURN;
    ELSE
        -- Reset to NULL if filters don't match
        mapped_section_key := NULL;
        config_section_info := NULL;
    END IF;
END;
$$;

/**
 * Process ADAD section for lifeInsuranceADAD group
 * Returns ADAD section config info if valid
 */
CREATE OR REPLACE FUNCTION sandf.get_adad_section_config(
    group_name TEXT,
    config_plan_details JSONB,
    OUT config_section_info JSONB
)
LANGUAGE plpgsql
AS $$
BEGIN
    config_section_info := NULL;

    -- Handle ADAD section separately for lifeInsuranceADAD
    IF group_name = 'lifeInsuranceADAD' AND config_plan_details ? 'ADAD' THEN
        config_section_info := config_plan_details -> 'ADAD';
    END IF;
END;
$$;

/**
 * Update benefit map with new benefit data
 * Returns updated benefit_map JSONB
 */
CREATE OR REPLACE FUNCTION sandf.update_benefit_map(
    benefit_map JSONB,
    benefit_key_item TEXT,
    benefit_name TEXT,
    benefit_key TEXT,
    section_key TEXT,
    display_carrier_name TEXT,
    carrier_value TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
BEGIN
    -- Initialize benefit if not exists
    IF NOT benefit_map ? benefit_key_item THEN
        benefit_map := benefit_map || jsonb_build_object(
            benefit_key_item,
            jsonb_build_object(
                'name', benefit_name,
                'key', benefit_key,
                'section', section_key,
                'values', '{}'::jsonb
            )
        );
    END IF;

    -- Update the carrier value
    benefit_map := jsonb_set(
        benefit_map,
        ARRAY[benefit_key_item, 'values', display_carrier_name],
        to_jsonb(carrier_value)
    );

    RETURN benefit_map;
END;
$$;

-- =====================================================
-- 6. PAGINATION AND RESULT BUILDING FUNCTIONS
-- =====================================================

/**
 * Build sections array from benefit map with skip logic applied
 * Returns JSONB array of sections with benefits
 */
CREATE OR REPLACE FUNCTION sandf.build_sections_with_skip_logic(
    section_map JSONB,
    benefit_map JSONB,
    config_plan_details JSONB,
    skip_fields TEXT[],
    coverage_life_values JSONB,
    field_values_to_check JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    all_sections JSONB := '[]'::jsonb;
    section_order_record RECORD;
    benefit_order_record RECORD;
    section_name TEXT;
    section_id TEXT;
    section_display_name TEXT;
    benefits_array JSONB;
    section_obj JSONB;
    benefit_obj JSONB;
    should_skip_field BOOLEAN;
BEGIN
    -- Build sections with proper ordering
    FOR section_order_record IN
        SELECT
            key as section_name,
            COALESCE(
                (config_plan_details -> key ->> 'order')::integer,
                999999
            ) as sort_order
        FROM jsonb_each_text(section_map)
        ORDER BY sort_order ASC, key ASC
    LOOP
        section_name := section_order_record.section_name;
        section_id := lower(replace(section_name, ' ', ''));
        section_display_name := COALESCE(section_map ->> section_name, section_name);
        benefits_array := '[]'::jsonb;

        -- Order benefits within each section by display_order
        FOR benefit_order_record IN
            SELECT
                key as benefit_key_item,
                value as benefit_obj,
                COALESCE(uf.display_order, 999999) as sort_order
            FROM jsonb_each(benefit_map)
            LEFT JOIN sandf.ui_field uf ON uf.name = (value ->> 'key')
            WHERE (value ->> 'section') = section_name
            ORDER BY sort_order ASC, (value ->> 'key') ASC
        LOOP
            benefit_obj := benefit_order_record.benefit_obj;

            -- Apply skip condition logic
            should_skip_field := sandf.should_skip_field(
                benefit_obj ->> 'key',
                skip_fields,
                coverage_life_values,
                field_values_to_check
            );

            -- Only add benefit if it shouldn't be skipped
            IF NOT should_skip_field THEN
                benefits_array := benefits_array || jsonb_build_array(benefit_obj);
            END IF;
        END LOOP;

        section_obj := jsonb_build_object(
            'name', section_display_name,
            'id', section_id,
            'benefits', benefits_array
        );

        all_sections := all_sections || jsonb_build_array(section_obj);
    END LOOP;

    RETURN all_sections;
END;
$$;

/**
 * Count total benefits across all sections
 * Returns integer count of total benefits
 */
CREATE OR REPLACE FUNCTION sandf.count_total_benefits(all_sections JSONB)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    total_benefits INTEGER := 0;
    section_idx INTEGER;
BEGIN
    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        total_benefits := total_benefits + jsonb_array_length(all_sections -> section_idx -> 'benefits');
    END LOOP;

    RETURN total_benefits;
END;
$$;

/**
 * Build single page result when benefits fit in one page
 * Returns JSONB array with single page object
 */
CREATE OR REPLACE FUNCTION sandf.build_single_page_result(
    ordered_carriers_array JSONB,
    all_sections JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN jsonb_build_array(
        jsonb_build_object(
            'carriers', ordered_carriers_array,
            'sections', all_sections
        )
    );
END;
$$;

-- =====================================================
-- 3. VALIDATION AND UTILITY FUNCTIONS
-- =====================================================

/**
 * Get display carrier name (Current vs actual name)
 * Returns "Current" for first carrier, actual name for others
 */
CREATE OR REPLACE FUNCTION sandf.get_display_carrier_name(
    carrier_name TEXT,
    first_carrier_name TEXT
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
    -- Use "Current" for first carrier, otherwise use actual name
    IF carrier_name = first_carrier_name THEN
        RETURN 'Current';
    ELSE
        RETURN carrier_name;
    END IF;
END;
$$;

/**
 * Validate and clean carrier value
 * Returns cleaned value or '-' if invalid
 */
CREATE OR REPLACE FUNCTION sandf.validate_carrier_value(carrier_value TEXT)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
BEGIN
    IF carrier_value IS NULL OR trim(carrier_value) = '' THEN
        RETURN '-';
    ELSE
        -- Validate as numeric if appropriate
        BEGIN
            PERFORM sandf.safe_parse_numeric(carrier_value);
            RETURN carrier_value;
        EXCEPTION WHEN OTHERS THEN
            RETURN '-';
        END;
    END IF;
END;
$$;

-- =====================================================
-- 4. SKIP CONDITION LOGIC FUNCTIONS
-- =====================================================

/**
 * Check if field should be skipped based on skip condition logic
 * Returns TRUE if field should be skipped, FALSE otherwise
 */
CREATE OR REPLACE FUNCTION sandf.should_skip_field(
    benefit_key TEXT,
    skip_fields TEXT[],
    coverage_life_values JSONB,
    field_values_to_check JSONB
)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    should_skip_field BOOLEAN := FALSE;
    carrier_check TEXT;
    coverage_value TEXT;
    field_value TEXT;
BEGIN
    -- Check if this is one of the fields to be compared with coverageLife
    IF benefit_key = ANY(skip_fields) THEN
        should_skip_field := TRUE;

        -- Check if all carriers have the same value for this field AND coverageLife
        FOR carrier_check IN SELECT jsonb_object_keys(coverage_life_values)
        LOOP
            coverage_value := coverage_life_values ->> carrier_check;
            field_value := (field_values_to_check -> benefit_key) ->> carrier_check;

            -- If any carrier has different values, don't skip
            IF coverage_value != field_value THEN
                should_skip_field := FALSE;
                EXIT;
            END IF;
        END LOOP;
    END IF;

    RETURN should_skip_field;
END;
$$;

/**
 * Store coverage life values for skip condition checking
 * Updates the coverage_life_values JSONB with new carrier value
 */
CREATE OR REPLACE FUNCTION sandf.store_coverage_life_value(
    coverage_life_values JSONB,
    display_carrier_name TEXT,
    carrier_value TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN coverage_life_values || jsonb_build_object(display_carrier_name, carrier_value);
END;
$$;

/**
 * Store field values for skip condition checking
 * Updates the field_values_to_check JSONB with new field value
 */
CREATE OR REPLACE FUNCTION sandf.store_field_value_for_checking(
    field_values_to_check JSONB,
    benefit_key TEXT,
    display_carrier_name TEXT,
    carrier_value TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
BEGIN
    -- Initialize field key if not exists
    IF NOT field_values_to_check ? benefit_key THEN
        field_values_to_check := field_values_to_check || jsonb_build_object(benefit_key, '{}'::jsonb);
    END IF;

    -- Store the value
    RETURN jsonb_set(
        field_values_to_check,
        ARRAY[benefit_key, display_carrier_name],
        to_jsonb(carrier_value)
    );
END;
$$;

-- =====================================================
-- 5. SECTION AND BENEFIT PROCESSING FUNCTIONS
-- =====================================================

/**
 * Check if section should be processed based on configuration
 * Returns mapped section key and config info if valid, NULL otherwise
 */
CREATE OR REPLACE FUNCTION sandf.get_section_config_info(
    group_name TEXT,
    config_plan_details JSONB,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL
)
RETURNS TABLE(mapped_section_key TEXT, config_section_info JSONB)
LANGUAGE plpgsql
AS $$
DECLARE
    result_mapped_key TEXT := NULL;
    result_config_info JSONB := NULL;
BEGIN
    -- Map lifeInsuranceADAD to both lifeInsurance and ADAD sections
    IF group_name = 'lifeInsuranceADAD' THEN
        -- Process as lifeInsurance first
        IF config_plan_details ? 'lifeInsurance' THEN
            result_mapped_key := 'lifeInsurance';
            result_config_info := config_plan_details -> 'lifeInsurance';
        END IF;
    ELSIF config_plan_details ? group_name THEN
        result_mapped_key := group_name;
        result_config_info := config_plan_details -> group_name;
    END IF;

    -- Check includes/excludes filters
    IF result_config_info IS NOT NULL AND
       (includes_param IS NULL OR group_name = ANY(includes_param)) AND
       (excludes_param IS NULL OR NOT (group_name = ANY(excludes_param))) THEN

        mapped_section_key := result_mapped_key;
        config_section_info := result_config_info;
        RETURN NEXT;
    END IF;
END;
$$;

/**
 * Process ADAD section for lifeInsuranceADAD group
 * Returns ADAD section config info if valid
 */
CREATE OR REPLACE FUNCTION sandf.get_adad_section_config(
    group_name TEXT,
    config_plan_details JSONB
)
RETURNS TABLE(config_section_info JSONB)
LANGUAGE plpgsql
AS $$
BEGIN
    -- Handle ADAD section separately for lifeInsuranceADAD
    IF group_name = 'lifeInsuranceADAD' AND config_plan_details ? 'ADAD' THEN
        config_section_info := config_plan_details -> 'ADAD';
        RETURN NEXT;
    END IF;
END;
$$;

/**
 * Update benefit map with new benefit data
 * Returns updated benefit_map JSONB
 */
CREATE OR REPLACE FUNCTION sandf.update_benefit_map(
    benefit_map JSONB,
    benefit_key_item TEXT,
    benefit_name TEXT,
    benefit_key TEXT,
    section_key TEXT,
    display_carrier_name TEXT,
    carrier_value TEXT
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
BEGIN
    -- Initialize benefit if not exists
    IF NOT benefit_map ? benefit_key_item THEN
        benefit_map := benefit_map || jsonb_build_object(
            benefit_key_item,
            jsonb_build_object(
                'name', benefit_name,
                'key', benefit_key,
                'section', section_key,
                'values', '{}'::jsonb
            )
        );
    END IF;

    -- Update the carrier value
    benefit_map := jsonb_set(
        benefit_map,
        ARRAY[benefit_key_item, 'values', display_carrier_name],
        to_jsonb(carrier_value)
    );

    RETURN benefit_map;
END;
$$;

-- =====================================================
-- 6. PAGINATION AND RESULT BUILDING FUNCTIONS
-- =====================================================

/**
 * Build sections array from benefit map with skip logic applied
 * Returns JSONB array of sections with benefits
 */
CREATE OR REPLACE FUNCTION sandf.build_sections_with_skip_logic(
    section_map JSONB,
    benefit_map JSONB,
    config_plan_details JSONB,
    skip_fields TEXT[],
    coverage_life_values JSONB,
    field_values_to_check JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    all_sections JSONB := '[]'::jsonb;
    section_order_record RECORD;
    benefit_order_record RECORD;
    section_name TEXT;
    section_id TEXT;
    section_display_name TEXT;
    benefits_array JSONB;
    section_obj JSONB;
    benefit_obj JSONB;
    should_skip_field BOOLEAN;
BEGIN
    -- Build sections with proper ordering
    FOR section_order_record IN
        SELECT
            key as section_name,
            COALESCE(
                (config_plan_details -> key ->> 'order')::integer,
                999999
            ) as sort_order
        FROM jsonb_each_text(section_map)
        ORDER BY sort_order ASC, key ASC
    LOOP
        section_name := section_order_record.section_name;
        section_id := lower(replace(section_name, ' ', ''));
        section_display_name := COALESCE(section_map ->> section_name, section_name);
        benefits_array := '[]'::jsonb;

        -- Order benefits within each section by display_order
        FOR benefit_order_record IN
            SELECT
                key as benefit_key_item,
                value as benefit_obj,
                COALESCE(uf.display_order, 999999) as sort_order
            FROM jsonb_each(benefit_map)
            LEFT JOIN sandf.ui_field uf ON uf.name = (value ->> 'key')
            WHERE (value ->> 'section') = section_name
            ORDER BY sort_order ASC, (value ->> 'key') ASC
        LOOP
            benefit_obj := benefit_order_record.benefit_obj;

            -- Apply skip condition logic
            should_skip_field := sandf.should_skip_field(
                benefit_obj ->> 'key',
                skip_fields,
                coverage_life_values,
                field_values_to_check
            );

            -- Only add benefit if it shouldn't be skipped
            IF NOT should_skip_field THEN
                benefits_array := benefits_array || jsonb_build_array(benefit_obj);
            END IF;
        END LOOP;

        section_obj := jsonb_build_object(
            'name', section_display_name,
            'id', section_id,
            'benefits', benefits_array
        );

        all_sections := all_sections || jsonb_build_array(section_obj);
    END LOOP;

    RETURN all_sections;
END;
$$;

/**
 * Count total benefits across all sections
 * Returns integer count of total benefits
 */
CREATE OR REPLACE FUNCTION sandf.count_total_benefits(all_sections JSONB)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    total_benefits INTEGER := 0;
    section_idx INTEGER;
BEGIN
    FOR section_idx IN 0..jsonb_array_length(all_sections)-1 LOOP
        total_benefits := total_benefits + jsonb_array_length(all_sections -> section_idx -> 'benefits');
    END LOOP;

    RETURN total_benefits;
END;
$$;

/**
 * Build single page result when benefits fit in one page
 * Returns JSONB array with single page object
 */
CREATE OR REPLACE FUNCTION sandf.build_single_page_result(
    ordered_carriers_array JSONB,
    all_sections JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN jsonb_build_array(
        jsonb_build_object(
            'carriers', ordered_carriers_array,
            'sections', all_sections
        )
    );
END;
$$;

-- =====================================================
-- 7. MULTI-CLASS SPECIFIC HELPER FUNCTIONS
-- =====================================================

/**
 * Get employee classes for a plan
 * Returns array of employee class names
 */
CREATE OR REPLACE FUNCTION sandf.get_employee_classes(plan_uuid_param TEXT)
RETURNS TEXT[]
LANGUAGE plpgsql
AS $$
DECLARE
    employee_classes TEXT[];
BEGIN
    SELECT array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb;

    RETURN COALESCE(employee_classes, ARRAY[]::TEXT[]);
END;
$$;

/**
 * Process multi-class section mapping
 * Returns updated section_map and section_original_names
 */
CREATE OR REPLACE FUNCTION sandf.process_multi_class_section_mapping(
    section_map JSONB,
    section_original_names JSONB,
    mapped_section_key TEXT,
    group_display_name TEXT,
    current_employee_class TEXT,
    class_suffix TEXT
)
RETURNS TABLE(
    updated_section_map JSONB,
    updated_section_original_names JSONB
)
LANGUAGE plpgsql
AS $$
BEGIN
    -- For multi-class, append class suffix to section mapping
    updated_section_map := section_map || jsonb_build_object(
        mapped_section_key || class_suffix,
        group_display_name || ' - ' || current_employee_class
    );

    -- Store original section name for ui_field lookup
    updated_section_original_names := section_original_names || jsonb_build_object(
        mapped_section_key || class_suffix,
        mapped_section_key
    );

    RETURN;
END;
$$;

/**
 * Process field details for multi-class sections
 * Returns updated benefit_map, coverage_life_values, and field_values_to_check
 */
CREATE OR REPLACE FUNCTION sandf.process_multi_class_field_details(
    field_details JSONB,
    config_display_items JSONB,
    mapped_section_key TEXT,
    class_suffix TEXT,
    carrier_name TEXT,
    first_carrier_name TEXT,
    skip_fields TEXT[],
    benefit_map JSONB,
    coverage_life_values JSONB,
    field_values_to_check JSONB
)
RETURNS TABLE(
    updated_benefit_map JSONB,
    updated_coverage_life_values JSONB,
    updated_field_values_to_check JSONB
)
LANGUAGE plpgsql
AS $$
DECLARE
    field_detail JSONB;
    benefit_key TEXT;
    carrier_value TEXT;
    benefit_name TEXT;
    display_carrier_name TEXT;
    benefit_key_item TEXT;
BEGIN
    -- Initialize return values
    updated_benefit_map := benefit_map;
    updated_coverage_life_values := coverage_life_values;
    updated_field_values_to_check := field_values_to_check;

    IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
        FOR field_detail IN SELECT jsonb_array_elements(field_details)
        LOOP
            benefit_key := field_detail ->> 'name';
            carrier_value := field_detail ->> 'value';

            -- Only process fields that are in displayItems configuration
            IF config_display_items ? benefit_key THEN
                benefit_name := config_display_items ->> benefit_key;

                -- Validate and clean carrier value using helper function
                carrier_value := sandf.validate_carrier_value(carrier_value);

                -- Get display carrier name using helper function
                display_carrier_name := sandf.get_display_carrier_name(carrier_name, first_carrier_name);

                -- Store coverage life values with class suffix
                IF benefit_key = 'coverageLife' THEN
                    updated_coverage_life_values := updated_coverage_life_values || jsonb_build_object(
                        display_carrier_name || '_' || class_suffix,
                        carrier_value
                    );
                END IF;

                -- Store values for skip condition checking with class suffix
                IF benefit_key = ANY(skip_fields) THEN
                    IF NOT updated_field_values_to_check ? (benefit_key || '_' || class_suffix) THEN
                        updated_field_values_to_check := updated_field_values_to_check || jsonb_build_object(
                            benefit_key || '_' || class_suffix,
                            '{}'::jsonb
                        );
                    END IF;
                    updated_field_values_to_check := jsonb_set(
                        updated_field_values_to_check,
                        ARRAY[benefit_key || '_' || class_suffix, display_carrier_name],
                        to_jsonb(carrier_value)
                    );
                END IF;

                benefit_key_item := mapped_section_key || class_suffix || '.' || benefit_key;

                -- Update benefit map with multi-class section naming
                IF NOT updated_benefit_map ? benefit_key_item THEN
                    updated_benefit_map := updated_benefit_map || jsonb_build_object(
                        benefit_key_item,
                        jsonb_build_object(
                            'name', benefit_name,
                            'key', benefit_key,
                            'section', lower(replace(replace(replace(mapped_section_key, ' ', ''), '&', ''), '''', '')) || '-' || lower(replace(replace(trim(class_suffix), ' ', ''), '''', '')),
                            'values', '{}'::jsonb
                        )
                    );
                END IF;

                updated_benefit_map := jsonb_set(
                    updated_benefit_map,
                    ARRAY[benefit_key_item, 'values', display_carrier_name],
                    to_jsonb(carrier_value)
                );
            END IF;
        END LOOP;
    END IF;

    RETURN;
END;
$$;

/**
 * Normalize benefit map to ensure all benefits have values for all carriers
 * Removes carriers without data and fills missing values with '-'
 */
CREATE OR REPLACE FUNCTION sandf.normalize_benefit_map_for_carriers(
    benefit_map JSONB,
    carrier_order_map JSONB
)
RETURNS TABLE(
    normalized_benefit_map JSONB,
    normalized_carrier_order_map JSONB
)
LANGUAGE plpgsql
AS $$
DECLARE
    all_carriers TEXT[];
    benefit_key_norm TEXT;
    carrier_name_norm TEXT;
    benefit_values_norm JSONB;
    carriers_with_data TEXT[] := '{}';
    carrier_has_any_data BOOLEAN;
    benefit_value TEXT;
BEGIN
    -- Initialize return values
    normalized_benefit_map := benefit_map;
    normalized_carrier_order_map := carrier_order_map;

    -- Get all unique carrier names
    SELECT array_agg(DISTINCT key ORDER BY key) INTO all_carriers
    FROM jsonb_each(carrier_order_map);

    -- First, identify carriers that have actual data (not just "-")
    FOREACH carrier_name_norm IN ARRAY all_carriers
    LOOP
        carrier_has_any_data := FALSE;

        FOR benefit_key_norm IN SELECT jsonb_object_keys(benefit_map)
        LOOP
            benefit_values_norm := benefit_map -> benefit_key_norm -> 'values';

            IF benefit_values_norm ? carrier_name_norm THEN
                benefit_value := benefit_values_norm ->> carrier_name_norm;
                IF benefit_value IS NOT NULL AND benefit_value != '-' AND trim(benefit_value) != '' THEN
                    carrier_has_any_data := TRUE;
                    EXIT;
                END IF;
            END IF;
        END LOOP;

        -- Only include carriers that have actual data
        IF carrier_has_any_data THEN
            carriers_with_data := array_append(carriers_with_data, carrier_name_norm);
        END IF;
    END LOOP;

    -- Update all_carriers to only include carriers with data
    all_carriers := carriers_with_data;

    -- For each benefit, ensure all carriers with data are present
    FOR benefit_key_norm IN SELECT jsonb_object_keys(benefit_map)
    LOOP
        benefit_values_norm := benefit_map -> benefit_key_norm -> 'values';

        -- Add missing carriers with "-" value
        FOREACH carrier_name_norm IN ARRAY all_carriers
        LOOP
            IF NOT (benefit_values_norm ? carrier_name_norm) THEN
                normalized_benefit_map := jsonb_set(
                    normalized_benefit_map,
                    ARRAY[benefit_key_norm, 'values', carrier_name_norm],
                    to_jsonb('-'::text)
                );
            END IF;
        END LOOP;

        -- Remove carriers that don't have data from this benefit
        FOR carrier_name_norm IN SELECT jsonb_object_keys(benefit_values_norm)
        LOOP
            IF NOT (carrier_name_norm = ANY(all_carriers)) THEN
                normalized_benefit_map := jsonb_set(
                    normalized_benefit_map,
                    ARRAY[benefit_key_norm, 'values'],
                    (normalized_benefit_map -> benefit_key_norm -> 'values') - carrier_name_norm
                );
            END IF;
        END LOOP;
    END LOOP;

    -- Update carrier_order_map to only include carriers with data
    FOR carrier_name_norm IN SELECT jsonb_object_keys(carrier_order_map)
    LOOP
        IF NOT (carrier_name_norm = ANY(all_carriers)) THEN
            normalized_carrier_order_map := normalized_carrier_order_map - carrier_name_norm;
        END IF;
    END LOOP;

    RETURN;
END;
$$;

-- =====================================================
-- 8. GROUPED MULTI-CLASS SPECIFIC HELPER FUNCTIONS
-- =====================================================

/**
 * Build class-to-letter mapping for grouped multi-class logic
 * Returns class_to_letter_map and class_legend_array
 */
CREATE OR REPLACE FUNCTION sandf.build_class_letter_mapping(employee_classes TEXT[])
RETURNS TABLE(
    class_to_letter_map JSONB,
    class_legend_array TEXT[]
)
LANGUAGE plpgsql
AS $$
DECLARE
    current_employee_class TEXT;
    current_letter TEXT;
    letter_index INTEGER := 1;
    result_map JSONB := '{}'::jsonb;
    result_legend TEXT[] := ARRAY[]::TEXT[];
BEGIN
    -- Build class-to-letter mapping (A, B, C, etc.) for groupby logic
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        current_letter := chr(64 + letter_index); -- A=65, B=66, etc.
        result_map := result_map || jsonb_build_object(current_employee_class, current_letter);
        result_legend := array_append(result_legend, current_letter || ' = ' || current_employee_class);
        letter_index := letter_index + 1;
    END LOOP;

    class_to_letter_map := result_map;
    class_legend_array := result_legend;
    RETURN;
END;
$$;

/**
 * Process class sections for grouped multi-class logic
 * Returns updated class_sections_map
 */
CREATE OR REPLACE FUNCTION sandf.process_class_section_data(
    plan_uuid_param TEXT,
    current_employee_class TEXT,
    carrier_order_map JSONB,
    config_plan_details JSONB,
    includes_param TEXT[] DEFAULT NULL,
    excludes_param TEXT[] DEFAULT NULL,
    first_carrier_name TEXT DEFAULT NULL,
    skip_fields TEXT[] DEFAULT ARRAY['maximumLife', 'maximumADAD']
)
RETURNS TABLE(
    class_sections_map JSONB,
    coverage_life_values JSONB,
    field_values_to_check JSONB
)
LANGUAGE plpgsql
AS $$
DECLARE
    quote_record RECORD;
    plan_details JSONB;
    fields JSONB;
    field_item JSONB;
    group_name TEXT;
    mapped_section_key TEXT;
    config_section_info JSONB;
    group_display_name TEXT;
    config_display_items JSONB;
    field_details JSONB;
    field_detail JSONB;
    benefit_key TEXT;
    carrier_value TEXT;
    benefit_name TEXT;
    carrier_name TEXT;
    display_carrier_name TEXT;
    result_class_sections_map JSONB := '{}'::jsonb;
    result_coverage_life_values JSONB := '{}'::jsonb;
    result_field_values_to_check JSONB := '{}'::jsonb;
    plan_details_key TEXT := 'planDetails';
BEGIN
    -- Initialize class sections map for this class
    result_class_sections_map := jsonb_build_object(current_employee_class, '{}'::jsonb);

    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = current_employee_class
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        ORDER BY
            COALESCE((carrier_order_map -> c.description ->> 'order')::integer, 999999) ASC,
            c.description ASC
    LOOP
        carrier_name := quote_record.carrier_description;
        plan_details := quote_record.formatted_quote_details -> plan_details_key;

        IF plan_details IS NOT NULL AND jsonb_typeof(plan_details) = 'object' THEN
            fields := plan_details -> 'fields';
            IF fields IS NOT NULL AND jsonb_typeof(fields) = 'array' THEN
                FOR field_item IN SELECT jsonb_array_elements(fields)
                LOOP
                    group_name := field_item ->> 'groupName';
                    IF group_name IS NULL THEN
                        group_name := field_item ->> 'name';
                    END IF;

                    -- Check if this section is in our configuration using helper function
                    SELECT * INTO mapped_section_key, config_section_info
                    FROM sandf.get_section_config_info(group_name, config_plan_details, includes_param, excludes_param);

                    -- Only process if section is in configuration
                    IF config_section_info IS NOT NULL THEN
                        group_display_name := config_section_info ->> mapped_section_key;
                        config_display_items := config_section_info -> 'displayItems';

                        -- Initialize section in class_sections_map if not exists
                        IF NOT (result_class_sections_map -> current_employee_class) ? mapped_section_key THEN
                            result_class_sections_map := jsonb_set(
                                result_class_sections_map,
                                ARRAY[current_employee_class, mapped_section_key],
                                jsonb_build_object(
                                    'name', group_display_name,
                                    'id', lower(replace(replace(replace(mapped_section_key, ' ', ''), '&', ''), '''', '')),
                                    'values', '{}'::jsonb
                                )
                            );
                        END IF;

                        field_details := field_item -> 'fields';
                        IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                            FOR field_detail IN SELECT jsonb_array_elements(field_details)
                            LOOP
                                benefit_key := field_detail ->> 'name';
                                carrier_value := field_detail ->> 'value';

                                -- Only process fields that are in displayItems configuration
                                IF config_display_items ? benefit_key THEN
                                    benefit_name := config_display_items ->> benefit_key;

                                    -- Validate and clean carrier value using helper function
                                    carrier_value := sandf.validate_carrier_value(carrier_value);

                                    -- Get display carrier name using helper function
                                    display_carrier_name := sandf.get_display_carrier_name(carrier_name, first_carrier_name);

                                    -- Store coverage life values with class suffix
                                    IF benefit_key = 'coverageLife' THEN
                                        result_coverage_life_values := result_coverage_life_values || jsonb_build_object(
                                            display_carrier_name || '_' || current_employee_class,
                                            carrier_value
                                        );
                                    END IF;

                                    -- Store values for skip condition checking with class suffix
                                    IF benefit_key = ANY(skip_fields) THEN
                                        IF NOT result_field_values_to_check ? (benefit_key || '_' || current_employee_class) THEN
                                            result_field_values_to_check := result_field_values_to_check || jsonb_build_object(
                                                benefit_key || '_' || current_employee_class,
                                                '{}'::jsonb
                                            );
                                        END IF;
                                        result_field_values_to_check := jsonb_set(
                                            result_field_values_to_check,
                                            ARRAY[benefit_key || '_' || current_employee_class, display_carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;

                                    -- Store benefit in class_sections_map structure
                                    -- Initialize benefit if not exists
                                    IF NOT (result_class_sections_map -> current_employee_class -> mapped_section_key -> 'values') ? benefit_key THEN
                                        result_class_sections_map := jsonb_set(
                                            result_class_sections_map,
                                            ARRAY[current_employee_class, mapped_section_key, 'values', benefit_key],
                                            jsonb_build_object(
                                                'name', benefit_name,
                                                'key', benefit_key,
                                                'values', '{}'::jsonb
                                            )
                                        );
                                    END IF;

                                    -- Set carrier value for this benefit
                                    result_class_sections_map := jsonb_set(
                                        result_class_sections_map,
                                        ARRAY[current_employee_class, mapped_section_key, 'values', benefit_key, 'values', display_carrier_name],
                                        to_jsonb(carrier_value)
                                    );
                                END IF;
                            END LOOP;
                        END IF;

                        -- Handle ADAD section separately for lifeInsuranceADAD using helper function
                        SELECT * INTO config_section_info FROM sandf.get_adad_section_config(group_name, config_plan_details);
                        IF config_section_info IS NOT NULL THEN
                            group_display_name := config_section_info ->> 'ADAD';
                            config_display_items := config_section_info -> 'displayItems';

                            -- Initialize ADAD section in class_sections_map if not exists
                            IF NOT (result_class_sections_map -> current_employee_class) ? 'ADAD' THEN
                                result_class_sections_map := jsonb_set(
                                    result_class_sections_map,
                                    ARRAY[current_employee_class, 'ADAD'],
                                    jsonb_build_object(
                                        'name', group_display_name,
                                        'id', 'adad',
                                        'values', '{}'::jsonb
                                    )
                                );
                            END IF;

                            -- Process ADAD fields
                            field_details := field_item -> 'fields';
                            IF field_details IS NOT NULL AND jsonb_typeof(field_details) = 'array' THEN
                                FOR field_detail IN SELECT jsonb_array_elements(field_details)
                                LOOP
                                    benefit_key := field_detail ->> 'name';
                                    carrier_value := field_detail ->> 'value';

                                    -- Only process ADAD fields that are in displayItems configuration
                                    IF config_display_items ? benefit_key THEN
                                        benefit_name := config_display_items ->> benefit_key;

                                        -- Validate and clean carrier value using helper function
                                        carrier_value := sandf.validate_carrier_value(carrier_value);

                                        -- Get display carrier name using helper function
                                        display_carrier_name := sandf.get_display_carrier_name(carrier_name, first_carrier_name);

                                        -- Store values for skip condition checking with class suffix
                                        IF benefit_key = ANY(skip_fields) THEN
                                            IF NOT result_field_values_to_check ? (benefit_key || '_' || current_employee_class) THEN
                                                result_field_values_to_check := result_field_values_to_check || jsonb_build_object(
                                                    benefit_key || '_' || current_employee_class,
                                                    '{}'::jsonb
                                                );
                                            END IF;
                                            result_field_values_to_check := jsonb_set(
                                                result_field_values_to_check,
                                                ARRAY[benefit_key || '_' || current_employee_class, display_carrier_name],
                                                to_jsonb(carrier_value)
                                            );
                                        END IF;

                                        -- Store ADAD benefit in class_sections_map structure
                                        -- Initialize benefit if not exists
                                        IF NOT (result_class_sections_map -> current_employee_class -> 'ADAD' -> 'values') ? benefit_key THEN
                                            result_class_sections_map := jsonb_set(
                                                result_class_sections_map,
                                                ARRAY[current_employee_class, 'ADAD', 'values', benefit_key],
                                                jsonb_build_object(
                                                    'name', benefit_name,
                                                    'key', benefit_key,
                                                    'values', '{}'::jsonb
                                                )
                                            );
                                        END IF;

                                        -- Set carrier value for this ADAD benefit
                                        result_class_sections_map := jsonb_set(
                                            result_class_sections_map,
                                            ARRAY[current_employee_class, 'ADAD', 'values', benefit_key, 'values', display_carrier_name],
                                            to_jsonb(carrier_value)
                                        );
                                    END IF;
                                END LOOP;
                            END IF;
                        END IF;
                    END IF;
                END LOOP;
            END IF;
        END IF;
    END LOOP;

    class_sections_map := result_class_sections_map;
    coverage_life_values := result_coverage_life_values;
    field_values_to_check := result_field_values_to_check;
    RETURN;
END;
$$;

/**
 * Build grouped sections result from class_sections_map
 * Returns final sections array with grouped benefits
 */
CREATE OR REPLACE FUNCTION sandf.build_grouped_sections_result(
    class_sections_map JSONB,
    employee_classes TEXT[],
    class_to_letter_map JSONB,
    config_plan_details JSONB,
    skip_fields TEXT[],
    coverage_life_values JSONB,
    field_values_to_check JSONB
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    all_sections JSONB := '[]'::jsonb;
    section_order_record RECORD;
    benefit_order_record RECORD;
    section_name TEXT;
    section_id TEXT;
    section_display_name TEXT;
    benefits_array JSONB;
    section_obj JSONB;
    benefit_obj JSONB;
    should_skip_field BOOLEAN;
    current_employee_class TEXT;
    current_letter TEXT;
    benefit_key TEXT;
    benefit_name TEXT;
    benefit_values JSONB;
    grouped_benefit_values JSONB;
    carrier_name TEXT;
    carrier_value TEXT;
    grouped_value TEXT;
    class_values TEXT[];
    class_value TEXT;
BEGIN
    -- Get all unique section names across all classes
    FOR section_order_record IN
        SELECT DISTINCT section_key as section_name,
               COALESCE(
                   (config_plan_details -> section_key ->> 'order')::integer,
                   999999
               ) as sort_order
        FROM (
            SELECT jsonb_object_keys(class_sections) as section_key
            FROM jsonb_each(class_sections_map) as class_data(class_name, class_sections)
        ) sections
        ORDER BY sort_order ASC, section_name ASC
    LOOP
        section_name := section_order_record.section_name;
        section_id := lower(replace(replace(replace(section_name, ' ', ''), '&', ''), '''', ''));

        -- Get section display name from first class that has this section
        section_display_name := section_name;
        FOREACH current_employee_class IN ARRAY employee_classes
        LOOP
            IF (class_sections_map -> current_employee_class) ? section_name THEN
                section_display_name := class_sections_map -> current_employee_class -> section_name ->> 'name';
                EXIT;
            END IF;
        END LOOP;

        benefits_array := '[]'::jsonb;

        -- Get all unique benefit keys for this section across all classes
        FOR benefit_order_record IN
            SELECT DISTINCT benefit_key,
                   COALESCE(uf.display_order, 999999) as sort_order
            FROM (
                SELECT jsonb_object_keys(class_sections_map -> class_name -> section_name -> 'values') as benefit_key
                FROM jsonb_object_keys(class_sections_map) as class_name
                WHERE (class_sections_map -> class_name) ? section_name
            ) benefits
            LEFT JOIN sandf.ui_field uf ON uf.name = benefit_key
            ORDER BY sort_order ASC, benefit_key ASC
        LOOP
            benefit_key := benefit_order_record.benefit_key;

            -- Get benefit name from first class that has this benefit
            benefit_name := benefit_key;
            FOREACH current_employee_class IN ARRAY employee_classes
            LOOP
                IF (class_sections_map -> current_employee_class) ? section_name AND
                   (class_sections_map -> current_employee_class -> section_name -> 'values') ? benefit_key THEN
                    benefit_name := class_sections_map -> current_employee_class -> section_name -> 'values' -> benefit_key ->> 'name';
                    EXIT;
                END IF;
            END LOOP;

            -- Apply skip condition logic for grouped benefits
            should_skip_field := FALSE;
            IF benefit_key = ANY(skip_fields) THEN
                should_skip_field := TRUE;
                -- Check across all classes if values match coverage life
                FOREACH current_employee_class IN ARRAY employee_classes
                LOOP
                    IF coverage_life_values ? ('Current_' || current_employee_class) AND
                       field_values_to_check ? (benefit_key || '_' || current_employee_class) THEN
                        IF (coverage_life_values ->> ('Current_' || current_employee_class)) !=
                           (field_values_to_check -> (benefit_key || '_' || current_employee_class) ->> 'Current') THEN
                            should_skip_field := FALSE;
                            EXIT;
                        END IF;
                    END IF;
                END LOOP;
            END IF;

            -- Only add benefit if it shouldn't be skipped
            IF NOT should_skip_field THEN
                grouped_benefit_values := '{}'::jsonb;

                -- Get all unique carrier names across all classes for this benefit
                FOR carrier_name IN
                    SELECT DISTINCT carrier_key
                    FROM (
                        SELECT jsonb_object_keys(class_sections_map -> class_name -> section_name -> 'values' -> benefit_key -> 'values') as carrier_key
                        FROM jsonb_object_keys(class_sections_map) as class_name
                        WHERE (class_sections_map -> class_name) ? section_name AND
                              (class_sections_map -> class_name -> section_name -> 'values') ? benefit_key
                    ) carriers
                    ORDER BY carrier_key
                LOOP
                    class_values := ARRAY[]::TEXT[];

                    -- Collect values from all classes for this carrier
                    FOREACH current_employee_class IN ARRAY employee_classes
                    LOOP
                        current_letter := class_to_letter_map ->> current_employee_class;

                        IF (class_sections_map -> current_employee_class) ? section_name AND
                           (class_sections_map -> current_employee_class -> section_name -> 'values') ? benefit_key AND
                           (class_sections_map -> current_employee_class -> section_name -> 'values' -> benefit_key -> 'values') ? carrier_name THEN

                            class_value := class_sections_map -> current_employee_class -> section_name -> 'values' -> benefit_key -> 'values' ->> carrier_name;
                            class_values := array_append(class_values, current_letter || ': ' || class_value);
                        ELSE
                            class_values := array_append(class_values, current_letter || ': -');
                        END IF;
                    END LOOP;

                    -- Join class values with line breaks
                    grouped_value := array_to_string(class_values, E'\n');
                    grouped_benefit_values := grouped_benefit_values || jsonb_build_object(carrier_name, grouped_value);
                END LOOP;

                benefit_obj := jsonb_build_object(
                    'name', benefit_name,
                    'key', benefit_key,
                    'values', grouped_benefit_values
                );

                benefits_array := benefits_array || jsonb_build_array(benefit_obj);
            END IF;
        END LOOP;

        section_obj := jsonb_build_object(
            'name', section_display_name,
            'id', section_id,
            'benefits', benefits_array
        );

        all_sections := all_sections || jsonb_build_array(section_obj);
    END LOOP;

    RETURN all_sections;
END;
$$;