[{"carriers": ["Current", "Manulife", "Victor"], "sections": [{"id": "lifeinsurance-manager", "name": "Life Insurance - manager", "benefits": [{"key": "coverageLife", "name": "Coverage LI", "values": {"Victor": "1 times annual earnings", "Current": "$100,000", "Manulife": "$25,000"}, "section": "lifeinsurance-manager"}, {"key": "ageReduction", "name": "Age Reduction", "values": {"Victor": "50% at age 65", "Current": "50% at age 65", "Manulife": "50% at age 65"}, "section": "lifeinsurance-manager"}, {"key": "terminationAgeLife", "name": "Termination Age LI", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75"}, "section": "lifeinsurance-manager"}]}, {"id": "lifeinsurance-production", "name": "Life Insurance - production", "benefits": [{"key": "coverageLife", "name": "Coverage LI", "values": {"Victor": "1 times annual earnings", "Current": "$50,000", "Manulife": "$25,000"}, "section": "lifeinsurance-production"}, {"key": "ageReduction", "name": "Age Reduction", "values": {"Victor": "50% at age 65", "Current": "50% at age 65", "Manulife": "50% at age 65"}, "section": "lifeinsurance-production"}, {"key": "terminationAgeLife", "name": "Termination Age LI", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75"}, "section": "lifeinsurance-production"}]}, {"id": "adad-manager", "name": "Accidental Death & Dismembermenet - manager", "benefits": [{"key": "terminationAgeADAD", "name": "Termination Age ADAD", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 71", "Manulife": "Retirement or to age 71"}, "section": "adad-manager"}]}, {"id": "adad-production", "name": "Accidental Death & Dismembermenet - production", "benefits": [{"key": "terminationAgeADAD", "name": "Termination Age ADAD", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 71", "Manulife": "Retirement or to age 71"}, "section": "adad-production"}]}]}, {"carriers": ["Current", "Manulife", "Victor"], "sections": [{"id": "dependentlife-manager", "name": "Dependent Life - manager", "benefits": [{"key": "spouse", "name": "Spouse", "values": {"Victor": "$10,000", "Current": "$10,000", "Manulife": "$5,000"}, "section": "dependentlife-manager"}, {"key": "child", "name": "Child", "values": {"Victor": "$5,000", "Current": "$5,000", "Manulife": "$2,500"}, "section": "dependentlife-manager"}]}, {"id": "dependentlife-production", "name": "Dependent Life - production", "benefits": [{"key": "spouse", "name": "Spouse", "values": {"Victor": "$10,000", "Current": "$10,000", "Manulife": "$5,000"}, "section": "dependentlife-production"}, {"key": "child", "name": "Child", "values": {"Victor": "$5,000", "Current": "$5,000", "Manulife": "$2,500"}, "section": "dependentlife-production"}]}]}, {"carriers": ["Current", "Manulife", "Victor"], "sections": [{"id": "longtermdisability-manager", "name": "Long Term Disability - manager", "benefits": [{"key": "coverageLTD", "name": "Coverage LTD", "values": {"Victor": "66.67%", "Current": "66.7%", "Manulife": "-"}, "section": "longtermdisability-manager"}, {"key": "eliminationPeriod", "name": "Elimination Period LTD", "values": {"Victor": "16 weeks", "Current": "119 days", "Manulife": "182 days"}, "section": "longtermdisability-manager"}, {"key": "benefitPeriod", "name": "Benefit Period LTD", "values": {"Victor": "to age 65", "Current": "to age 65", "Manulife": "-"}, "section": "longtermdisability-manager"}, {"key": "benefitMaximumLTD", "name": "Benefit Maximum LTD", "values": {"Victor": "$5,000", "Current": "$5,000", "Manulife": "-"}, "section": "longtermdisability-manager"}, {"key": "definitionOfDisability", "name": "Definition of Disability", "values": {"Victor": "2 year own occupation", "Current": "2 year own occupation", "Manulife": "any occupation"}, "section": "longtermdisability-manager"}, {"key": "terminationAgeLTD", "name": "Termination Age LTD", "values": {"Victor": "Retirement or to age 65", "Current": "Retirement or to age 65", "Manulife": "Retirement or to age 65"}, "section": "longtermdisability-manager"}, {"key": "nonEvidenceMaximum", "name": "Non Evidence Maximum", "values": {"Victor": "$4,500", "Current": "$3,500", "Manulife": "-"}, "section": "longtermdisability-manager"}]}]}, {"carriers": ["Current", "Manulife", "Victor"], "sections": [{"id": "longtermdisability-production", "name": "Long Term Disability - production", "benefits": [{"key": "coverageLTD", "name": "Coverage LTD", "values": {"Victor": "66.67%", "Current": "66.7%", "Manulife": "-"}, "section": "longtermdisability-production"}, {"key": "eliminationPeriod", "name": "Elimination Period LTD", "values": {"Victor": "16 weeks", "Current": "119 days", "Manulife": "182 days"}, "section": "longtermdisability-production"}, {"key": "benefitPeriod", "name": "Benefit Period LTD", "values": {"Victor": "to age 65", "Current": "to age 65", "Manulife": "-"}, "section": "longtermdisability-production"}, {"key": "benefitMaximumLTD", "name": "Benefit Maximum LTD", "values": {"Victor": "$5,000", "Current": "$5,000", "Manulife": "-"}, "section": "longtermdisability-production"}, {"key": "definitionOfDisability", "name": "Definition of Disability", "values": {"Victor": "2 year own occupation", "Current": "2 year own occupation", "Manulife": "any occupation"}, "section": "longtermdisability-production"}, {"key": "terminationAgeLTD", "name": "Termination Age LTD", "values": {"Victor": "Retirement or to age 65", "Current": "Retirement or to age 65", "Manulife": "Retirement or to age 65"}, "section": "longtermdisability-production"}, {"key": "nonEvidenceMaximum", "name": "Non Evidence Maximum", "values": {"Victor": "$4,500", "Current": "$3,500", "Manulife": "$25,000"}, "section": "longtermdisability-production"}]}, {"id": "<PERSON><PERSON>ness-manager", "name": "Critical Illness - manager", "benefits": [{"key": "amountCI", "name": "Amount", "values": {"Victor": "-", "Current": "-", "Manulife": "$30,000"}, "section": "<PERSON><PERSON>ness-manager"}, {"key": "coverageCI", "name": "Coverage", "values": {"Victor": "-", "Current": "-", "Manulife": "Employee Only"}, "section": "<PERSON><PERSON>ness-manager"}, {"key": "terminationAgeCI", "name": "Termination Age", "values": {"Victor": "-", "Current": "-", "Manulife": "Retirement or to age 70"}, "section": "<PERSON><PERSON>ness-manager"}]}]}, {"carriers": ["Current", "Manulife", "Victor"], "sections": [{"id": "criticalillness-production", "name": "Critical Illness - production", "benefits": [{"key": "amountCI", "name": "Amount", "values": {"Victor": "-", "Current": "-", "Manulife": "$30,000"}, "section": "criticalillness-production"}, {"key": "coverageCI", "name": "Coverage", "values": {"Victor": "-", "Current": "-", "Manulife": "Employee Only"}, "section": "criticalillness-production"}, {"key": "terminationAgeCI", "name": "Termination Age", "values": {"Victor": "-", "Current": "-", "Manulife": "Retirement or to age 70"}, "section": "criticalillness-production"}]}, {"id": "employeeassistance-manager", "name": "Employee Assistance - manager", "benefits": [{"key": "coverageEA", "name": "Coverage", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes"}, "section": "employeeassistance-manager"}]}, {"id": "employeeassistance-production", "name": "Employee Assistance - production", "benefits": [{"key": "coverageEA", "name": "Coverage", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes"}, "section": "employeeassistance-production"}]}]}, {"carriers": ["Current", "Manulife", "Victor"], "sections": [{"id": "extendedhealth-manager", "name": "Extended Health - manager", "benefits": [{"key": "annualDeductibleEHC", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Current": "<PERSON>l", "Manulife": "<PERSON>l"}, "section": "extendedhealth-manager"}, {"key": "paramedicalMaximum", "name": "Paramedical Maximum", "values": {"Victor": "$500", "Current": "$500", "Manulife": "$500"}, "section": "extendedhealth-manager"}, {"key": "visionCare", "name": "Vision", "values": {"Victor": "$250/24 months", "Current": "$200/2 years", "Manulife": "$200/2 years"}, "section": "extendedhealth-manager"}, {"key": "eyeExams", "name": "<PERSON>ams", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes"}, "section": "extendedhealth-manager"}, {"key": "privateDutyNursingMaximum", "name": "Private Duty Nursing Maximum", "values": {"Victor": "$10,000/year", "Current": "$10,000/year", "Manulife": "$10,000/year"}, "section": "extendedhealth-manager"}, {"key": "semiPrivateHospital", "name": "Semi Private Hospital", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes"}, "section": "extendedhealth-manager"}, {"key": "hearingAids", "name": "Hearing Aids", "values": {"Victor": "$500/3 years", "Current": "$500/5 years", "Manulife": "$500/5 years"}, "section": "extendedhealth-manager"}, {"key": "outOfCountryTravel", "name": "Out of Country Emergency", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes"}, "section": "extendedhealth-manager"}, {"key": "terminationAgeEHC", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75"}, "section": "extendedhealth-manager"}]}]}, {"carriers": ["Current", "Manulife", "Victor"], "sections": [{"id": "extendedhealth-production", "name": "Extended Health - production", "benefits": [{"key": "annualDeductibleEHC", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Current": "<PERSON>l", "Manulife": "<PERSON>l"}, "section": "extendedhealth-production"}, {"key": "paramedicalMaximum", "name": "Paramedical Maximum", "values": {"Victor": "$500", "Current": "$500", "Manulife": "$500"}, "section": "extendedhealth-production"}, {"key": "visionCare", "name": "Vision", "values": {"Victor": "$250/24 months", "Current": "$200/2 years", "Manulife": "$200/2 years"}, "section": "extendedhealth-production"}, {"key": "eyeExams", "name": "<PERSON>ams", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes"}, "section": "extendedhealth-production"}, {"key": "privateDutyNursingMaximum", "name": "Private Duty Nursing Maximum", "values": {"Victor": "$10,000/year", "Current": "$10,000/year", "Manulife": "$10,000/year"}, "section": "extendedhealth-production"}, {"key": "semiPrivateHospital", "name": "Semi Private Hospital", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes"}, "section": "extendedhealth-production"}, {"key": "hearingAids", "name": "Hearing Aids", "values": {"Victor": "$500/3 years", "Current": "$500/5 years", "Manulife": "$500/5 years"}, "section": "extendedhealth-production"}, {"key": "outOfCountryTravel", "name": "Out of Country Emergency", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes"}, "section": "extendedhealth-production"}, {"key": "terminationAgeEHC", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75"}, "section": "extendedhealth-production"}]}]}, {"carriers": ["Current", "Manulife", "Victor"], "sections": [{"id": "dental-manager", "name": "Dental - manager", "benefits": [{"key": "annualDeductibleDental", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Current": "<PERSON>l", "Manulife": "<PERSON>l"}, "section": "dental-manager"}, {"key": "basicCoInsurance", "name": "Basic Co-Insurance", "values": {"Victor": "80%", "Current": "80%", "Manulife": "80%"}, "section": "dental-manager"}, {"key": "majorCoInsurance", "name": "Major Co-Insurance", "values": {"Victor": "50%", "Current": "50%", "Manulife": "50%"}, "section": "dental-manager"}, {"key": "basicAndMajorDentalMaximum", "name": "Basic & Major Dental Maximum", "values": {"Victor": "$2,000", "Current": "$2,000", "Manulife": "$2,000"}, "section": "dental-manager"}, {"key": "dentalRecall", "name": "Recall", "values": {"Victor": "6 months", "Current": "6 months", "Manulife": "6 months"}, "section": "dental-manager"}, {"key": "terminationAgeDental", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75"}, "section": "dental-manager"}]}]}, {"carriers": ["Current", "Manulife", "Victor"], "sections": [{"id": "dental-production", "name": "Dental - production", "benefits": [{"key": "annualDeductibleDental", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Current": "<PERSON>l", "Manulife": "<PERSON>l"}, "section": "dental-production"}, {"key": "basicCoInsurance", "name": "Basic Co-Insurance", "values": {"Victor": "80%", "Current": "80%", "Manulife": "80%"}, "section": "dental-production"}, {"key": "majorCoInsurance", "name": "Major Co-Insurance", "values": {"Victor": "50%", "Current": "50%", "Manulife": "50%"}, "section": "dental-production"}, {"key": "basicAndMajorDentalMaximum", "name": "Basic & Major Dental Maximum", "values": {"Victor": "$2,000", "Current": "$2,000", "Manulife": "$2,000"}, "section": "dental-production"}, {"key": "dentalRecall", "name": "Recall", "values": {"Victor": "6 months", "Current": "6 months", "Manulife": "6 months"}, "section": "dental-production"}, {"key": "terminationAgeDental", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75"}, "section": "dental-production"}]}]}]