{"planDetails": {"ADAD": {"ADAD": "Accidental Death & Dismembermenet", "order": 2, "displayItems": {"maximumADAD": "Maximum AD&D", "terminationAgeADAD": "Termination Age ADAD"}}, "dental": {"order": 9, "dental": "Dental", "displayItems": {"dentalRecall": "Recall", "basicCoInsurance": "Basic Co-Insurance", "majorCoInsurance": "Major Co-Insurance", "terminationAgeDental": "Termination Age", "annualDeductibleDental": "Annual Deductible", "orthodonticCoInsurance": "Orthodontic Co-Insurance", "basicAndMajorDentalMaximum": "Basic & Major Dental Maximum", "orthodonticLifetimeMaximum": "Orthodontic Lifetime Maximum"}}, "dependentLife": {"order": 3, "displayItems": {"child": "Child", "spouse": "Spouse"}, "dependentLife": "Dependent Life"}, "lifeInsurance": {"order": 1, "displayItems": {"maximumLife": "Maximum LI", "ageReduction": "Age Reduction", "coverageLife": "Coverage LI", "terminationAgeLife": "Termination Age LI"}, "lifeInsurance": "Life Insurance"}, "extendedHealth": {"order": 8, "displayItems": {"eyeExams": "<PERSON>ams", "visionCare": "Vision", "CoInsurance": "Co-Insurance EHC", "hearingAids": "Hearing Aids", "terminationAgeEHC": "Termination Age", "outOfCountryTravel": "Out of Country Emergency", "paramedicalMaximum": "Paramedical Maximum", "annualDeductibleEHC": "Annual Deductible", "prescriptionMaximum": "Prescription Maximum", "semiPrivateHospital": "Semi Private Hospital", "prescriptionDrugType": "Prescription Drug Type", "privateDutyNursingMaximum": "Private Duty Nursing Maximum", "prescriptionDrugCoInsurance": "Prescription Drug Co-Insurance", "prescriptionPayDirectDrugCard": "Pay Direct Drug Card"}, "extendedHealth": "Extended Health"}, "criticalIllness": {"order": 6, "displayItems": {"amountCI": "Amount", "coverageCI": "Coverage", "terminationAgeCI": "Termination Age"}, "criticalIllness": "Critical Illness"}, "employeeAssistance": {"order": 7, "displayItems": {"coverageEA": "Coverage"}, "employeeAssistance": "Employee Assistance"}, "longTermDisability": {"order": 4, "displayItems": {"coverageLTD": "Coverage LTD", "benefitPeriod": "Benefit Period LTD", "quoteToNEMOrMax": "Quote to NEM or Max", "benefitMaximumLTD": "Benefit Maximum LTD", "eliminationPeriod": "Elimination Period LTD", "terminationAgeLTD": "Termination Age LTD", "nonEvidenceMaximum": "Non Evidence Maximum", "definitionOfDisability": "Definition of Disability"}, "longTermDisability": "Long Term Disability"}, "shortTermDisability": {"order": 5, "elimination": ""}}}