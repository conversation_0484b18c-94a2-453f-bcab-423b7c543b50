[{"carriers": ["Current", "Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "lifeinsuranceadad-manager", "name": "Life Insurance & AD&D - manager", "benefits": [{"key": "coverageLife", "name": "Amount of Coverage", "values": {"Victor": "1 times annual earnings", "Current": "$100,000", "Manulife": "$25,000", "Canada Life": "-", "Chambers Plan": "1 times annual earnings"}, "section": "lifeinsuranceadad-manager"}, {"key": "ageReduction", "name": "Age Reduction", "values": {"Victor": "50% at age 65", "Current": "50% at age 65", "Manulife": "50% at age 65", "Canada Life": "-", "Chambers Plan": "50% at age 65"}, "section": "lifeinsuranceadad-manager"}, {"key": "terminationAgeLife", "name": "Termination Age - Life", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75", "Canada Life": "-", "Chambers Plan": "Retirement or to age 75"}, "section": "lifeinsuranceadad-manager"}, {"key": "terminationAgeADAD", "name": "Termination Age - AD&D", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 71", "Manulife": "Retirement or to age 71", "Canada Life": "-", "Chambers Plan": "Retirement or to age 75"}, "section": "lifeinsuranceadad-manager"}]}, {"id": "lifeinsuranceadad-production", "name": "Life Insurance & AD&D - production", "benefits": [{"key": "coverageLife", "name": "Amount of Coverage", "values": {"Victor": "1 times annual earnings", "Current": "$50,000", "Manulife": "$25,000", "Canada Life": "-", "Chambers Plan": "1 times annual earnings"}, "section": "lifeinsuranceadad-production"}, {"key": "ageReduction", "name": "Age Reduction", "values": {"Victor": "50% at age 65", "Current": "50% at age 65", "Manulife": "50% at age 65", "Canada Life": "-", "Chambers Plan": "50% at age 65"}, "section": "lifeinsuranceadad-production"}, {"key": "terminationAgeLife", "name": "Termination Age - Life", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75", "Canada Life": "-", "Chambers Plan": "Retirement or to age 75"}, "section": "lifeinsuranceadad-production"}, {"key": "terminationAgeADAD", "name": "Termination Age - AD&D", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 71", "Manulife": "Retirement or to age 71", "Canada Life": "-", "Chambers Plan": "Retirement or to age 75"}, "section": "lifeinsuranceadad-production"}]}, {"id": "dependentlife-manager", "name": "Dependent Life - manager", "benefits": [{"key": "spouse", "name": "Spouse", "values": {"Victor": "$10,000", "Current": "$10,000", "Manulife": "$5,000", "Canada Life": "-", "Chambers Plan": "$3,200"}, "section": "dependentlife-manager"}, {"key": "child", "name": "Child", "values": {"Victor": "$5,000", "Current": "$5,000", "Manulife": "$2,500", "Canada Life": "-", "Chambers Plan": "$3,200"}, "section": "dependentlife-manager"}]}]}, {"carriers": ["Current", "Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "dependentlife-production", "name": "Dependent Life - production", "benefits": [{"key": "spouse", "name": "Spouse", "values": {"Victor": "$10,000", "Current": "$10,000", "Manulife": "$5,000", "Canada Life": "-", "Chambers Plan": "$3,200"}, "section": "dependentlife-production"}, {"key": "child", "name": "Child", "values": {"Victor": "$5,000", "Current": "$5,000", "Manulife": "$2,500", "Canada Life": "-", "Chambers Plan": "$2,600"}, "section": "dependentlife-production"}]}, {"id": "extendedhealth-manager", "name": "Extended Health - manager", "benefits": [{"key": "annualDeductibleEHC", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Current": "<PERSON>l", "Manulife": "<PERSON>l", "Canada Life": "-", "Chambers Plan": "<PERSON>l"}, "section": "extendedhealth-manager"}, {"key": "coInsuranceEHC", "name": "CoInsurance", "values": {"Victor": "100%", "Current": "100%", "Manulife": "100%", "Canada Life": "-", "Chambers Plan": "100%"}, "section": "extendedhealth-manager"}, {"key": "paramedicalMaximum", "name": "Paramedical Maximum", "values": {"Victor": "$500", "Current": "$500", "Manulife": "$500", "Canada Life": "-", "Chambers Plan": "$500"}, "section": "extendedhealth-manager"}, {"key": "<PERSON><PERSON><PERSON><PERSON>peechTherapist", "name": "Psychologist/Speech Therapist", "values": {"Victor": "$500", "Current": "$500", "Manulife": "$500", "Canada Life": "-", "Chambers Plan": "$600"}, "section": "extendedhealth-manager"}, {"key": "visionCare", "name": "Vision", "values": {"Victor": "$250/24 months", "Current": "$200/2 years", "Manulife": "$200/2 years", "Canada Life": "-", "Chambers Plan": "$200/24 months"}, "section": "extendedhealth-manager"}, {"key": "eyeExams", "name": "<PERSON>ams", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes", "Canada Life": "-", "Chambers Plan": "Yes"}, "section": "extendedhealth-manager"}, {"key": "privateDutyNursingMaximum", "name": "Private Duty Nursing Maximum", "values": {"Victor": "$10,000/year", "Current": "$10,000/year", "Manulife": "$10,000/year", "Canada Life": "-", "Chambers Plan": "$25,000/24 months"}, "section": "extendedhealth-manager"}, {"key": "semiPrivateHospital", "name": "Semi Private Hospital", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes", "Canada Life": "-", "Chambers Plan": "Yes"}, "section": "extendedhealth-manager"}]}]}, {"carriers": ["Current", "Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "extendedhealth-manager", "name": "Extended Health - manager", "benefits": [{"key": "hearingAids", "name": "Hearing Aids", "values": {"Victor": "$500/3 years", "Current": "$500/5 years", "Manulife": "$500/5 years", "Canada Life": "-", "Chambers Plan": "$700/60 months"}, "section": "extendedhealth-manager"}, {"key": "outOfCountryTravel", "name": "Out of Country Emergency", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes", "Canada Life": "-", "Chambers Plan": "Yes"}, "section": "extendedhealth-manager"}, {"key": "terminationAgeEHC", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75", "Canada Life": "-", "Chambers Plan": "Retirement or to age 80"}, "section": "extendedhealth-manager"}]}, {"id": "extendedhealth-production", "name": "Extended Health - production", "benefits": [{"key": "annualDeductibleEHC", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Current": "<PERSON>l", "Manulife": "<PERSON>l", "Canada Life": "-", "Chambers Plan": "<PERSON>l"}, "section": "extendedhealth-production"}, {"key": "coInsuranceEHC", "name": "CoInsurance", "values": {"Victor": "100%", "Current": "100%", "Manulife": "100%", "Canada Life": "-", "Chambers Plan": "100%"}, "section": "extendedhealth-production"}, {"key": "paramedicalMaximum", "name": "Paramedical Maximum", "values": {"Victor": "$500", "Current": "$500", "Manulife": "$500", "Canada Life": "-", "Chambers Plan": "$500"}, "section": "extendedhealth-production"}, {"key": "<PERSON><PERSON><PERSON><PERSON>peechTherapist", "name": "Psychologist/Speech Therapist", "values": {"Victor": "$500", "Current": "$500", "Manulife": "$500", "Canada Life": "-", "Chambers Plan": "$600"}, "section": "extendedhealth-production"}, {"key": "visionCare", "name": "Vision", "values": {"Victor": "$250/24 months", "Current": "$200/2 years", "Manulife": "$200/2 years", "Canada Life": "-", "Chambers Plan": "$200/24 months adults, 12 months children"}, "section": "extendedhealth-production"}, {"key": "eyeExams", "name": "<PERSON>ams", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes", "Canada Life": "-", "Chambers Plan": "Yes"}, "section": "extendedhealth-production"}, {"key": "privateDutyNursingMaximum", "name": "Private Duty Nursing Maximum", "values": {"Victor": "$10,000/year", "Current": "$10,000/year", "Manulife": "$10,000/year", "Canada Life": "-", "Chambers Plan": "$25,000/24 months"}, "section": "extendedhealth-production"}]}]}, {"carriers": ["Current", "Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "extendedhealth-production", "name": "Extended Health - production", "benefits": [{"key": "semiPrivateHospital", "name": "Semi Private Hospital", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes", "Canada Life": "-", "Chambers Plan": "Yes"}, "section": "extendedhealth-production"}, {"key": "hearingAids", "name": "Hearing Aids", "values": {"Victor": "$500/3 years", "Current": "$500/5 years", "Manulife": "$500/5 years", "Canada Life": "-", "Chambers Plan": "$700/60 months"}, "section": "extendedhealth-production"}, {"key": "outOfCountryTravel", "name": "Out of Country Emergency", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes", "Canada Life": "-", "Chambers Plan": "Yes"}, "section": "extendedhealth-production"}, {"key": "terminationAgeEHC", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75", "Canada Life": "-", "Chambers Plan": "Retirement or to age 80"}, "section": "extendedhealth-production"}]}, {"id": "prescriptiondrugs-manager", "name": "Prescription Drugs - manager", "benefits": [{"key": "drugDeductible", "name": "Drug Deductible", "values": {"Victor": "<PERSON>l", "Current": "-", "Manulife": "<PERSON>l", "Canada Life": "-", "Chambers Plan": "<PERSON>l"}, "section": "prescriptiondrugs-manager"}, {"key": "prescriptionDrugCoInsurance", "name": "Prescription Drug Co-Insurance", "values": {"Victor": "80%", "Current": "80%", "Manulife": "80%", "Canada Life": "-", "Chambers Plan": "100%/50%"}, "section": "prescriptiondrugs-manager"}, {"key": "prescriptionMaximum", "name": "Prescription Maximum", "values": {"Victor": "unlimited", "Current": "-", "Manulife": "$50,000", "Canada Life": "-", "Chambers Plan": "$50,000"}, "section": "prescriptiondrugs-manager"}, {"key": "prescriptionDrugType", "name": "Prescription Drug Type", "values": {"Victor": "Generic", "Current": "National Formulary", "Manulife": "Generic", "Canada Life": "-", "Chambers Plan": "National Formulary"}, "section": "prescriptiondrugs-manager"}, {"key": "prescriptionPayDirectDrugCard", "name": "Pay Direct Drug Card", "values": {"Victor": "Yes", "Current": "-", "Manulife": "Yes", "Canada Life": "-", "Chambers Plan": "Yes"}, "section": "prescriptiondrugs-manager"}, {"key": "reimbursement", "name": "Reimbursement Type", "values": {"Victor": "Pay Direct", "Current": "-", "Manulife": "Pay Direct", "Canada Life": "-", "Chambers Plan": "Pay Direct"}, "section": "prescriptiondrugs-manager"}]}]}, {"carriers": ["Current", "Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "prescriptiondrugs-production", "name": "Prescription Drugs - production", "benefits": [{"key": "drugDeductible", "name": "Drug Deductible", "values": {"Victor": "<PERSON>l", "Current": "-", "Manulife": "<PERSON>l", "Canada Life": "-", "Chambers Plan": "<PERSON>l"}, "section": "prescriptiondrugs-production"}, {"key": "prescriptionDrugCoInsurance", "name": "Prescription Drug Co-Insurance", "values": {"Victor": "80%", "Current": "80%", "Manulife": "80%", "Canada Life": "-", "Chambers Plan": "100%/50%"}, "section": "prescriptiondrugs-production"}, {"key": "prescriptionMaximum", "name": "Prescription Maximum", "values": {"Victor": "unlimited", "Current": "unlimited", "Manulife": "$50,000", "Canada Life": "-", "Chambers Plan": "$50,000"}, "section": "prescriptiondrugs-production"}, {"key": "prescriptionDrugType", "name": "Prescription Drug Type", "values": {"Victor": "Generic", "Current": "National Formulary", "Manulife": "Generic", "Canada Life": "-", "Chambers Plan": "National Formulary"}, "section": "prescriptiondrugs-production"}, {"key": "prescriptionPayDirectDrugCard", "name": "Pay Direct Drug Card", "values": {"Victor": "Yes", "Current": "-", "Manulife": "Yes", "Canada Life": "-", "Chambers Plan": "Yes"}, "section": "prescriptiondrugs-production"}, {"key": "reimbursement", "name": "Reimbursement Type", "values": {"Victor": "Pay Direct", "Current": "-", "Manulife": "Pay Direct", "Canada Life": "-", "Chambers Plan": "Pay Direct"}, "section": "prescriptiondrugs-production"}]}, {"id": "dental-manager", "name": "Dental - manager", "benefits": [{"key": "annualDeductibleDental", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Current": "<PERSON>l", "Manulife": "<PERSON>l", "Canada Life": "-", "Chambers Plan": "<PERSON>l"}, "section": "dental-manager"}, {"key": "basicCoInsurance", "name": "Basic Co-Insurance", "values": {"Victor": "80%", "Current": "80%", "Manulife": "80%", "Canada Life": "-", "Chambers Plan": "80%"}, "section": "dental-manager"}, {"key": "majorCoInsurance", "name": "Major Co-Insurance", "values": {"Victor": "50%", "Current": "50%", "Manulife": "50%", "Canada Life": "-", "Chambers Plan": "50%"}, "section": "dental-manager"}, {"key": "orthodonticCoInsurance", "name": "Orthodontic Co-Insurance", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "dental-manager"}]}]}, {"carriers": ["Current", "Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "dental-manager", "name": "Dental - manager", "benefits": [{"key": "basicDentalMaximum", "name": "Basic Dental Maximum", "values": {"Victor": "$2,000", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "dental-manager"}, {"key": "majorDentalMaximum", "name": "Major Dental Maximum", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "dental-manager"}, {"key": "basicAndMajorDentalMaximum", "name": "Basic and Major Dental Maximum", "values": {"Victor": "$2,000", "Current": "$2,000", "Manulife": "$2,000", "Canada Life": "-", "Chambers Plan": "$2,000"}, "section": "dental-manager"}, {"key": "orthodonticLifetimeMaximum", "name": "Orthodontic Lifetime Maximum", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "dental-manager"}, {"key": "dentalRecall", "name": "Dental Recall", "values": {"Victor": "6 months", "Current": "6 months", "Manulife": "6 months", "Canada Life": "-", "Chambers Plan": "2 times per year"}, "section": "dental-manager"}, {"key": "terminationAgeDental", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75", "Canada Life": "-", "Chambers Plan": "Retirement or to age 80"}, "section": "dental-manager"}]}, {"id": "dental-production", "name": "Dental - production", "benefits": [{"key": "annualDeductibleDental", "name": "Annual Deductible", "values": {"Victor": "<PERSON>l", "Current": "<PERSON>l", "Manulife": "<PERSON>l", "Canada Life": "-", "Chambers Plan": "<PERSON>l"}, "section": "dental-production"}, {"key": "basicCoInsurance", "name": "Basic Co-Insurance", "values": {"Victor": "80%", "Current": "80%", "Manulife": "80%", "Canada Life": "-", "Chambers Plan": "80%"}, "section": "dental-production"}, {"key": "majorCoInsurance", "name": "Major Co-Insurance", "values": {"Victor": "50%", "Current": "50%", "Manulife": "50%", "Canada Life": "-", "Chambers Plan": "50%"}, "section": "dental-production"}, {"key": "orthodonticCoInsurance", "name": "Orthodontic Co-Insurance", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "dental-production"}]}]}, {"carriers": ["Current", "Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "dental-production", "name": "Dental - production", "benefits": [{"key": "basicDentalMaximum", "name": "Basic Dental Maximum", "values": {"Victor": "$2,000", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "dental-production"}, {"key": "majorDentalMaximum", "name": "Major Dental Maximum", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "dental-production"}, {"key": "basicAndMajorDentalMaximum", "name": "Basic and Major Dental Maximum", "values": {"Victor": "$2,000", "Current": "$2,000", "Manulife": "$2,000", "Canada Life": "-", "Chambers Plan": "$2,000"}, "section": "dental-production"}, {"key": "orthodonticLifetimeMaximum", "name": "Orthodontic Lifetime Maximum", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "dental-production"}, {"key": "dentalRecall", "name": "Dental Recall", "values": {"Victor": "6 months", "Current": "6 months", "Manulife": "6 months", "Canada Life": "-", "Chambers Plan": "2 times per year"}, "section": "dental-production"}, {"key": "terminationAgeDental", "name": "Termination Age", "values": {"Victor": "Retirement or to age 70", "Current": "Retirement or to age 75", "Manulife": "Retirement or to age 75", "Canada Life": "-", "Chambers Plan": "Retirement or to age 80"}, "section": "dental-production"}]}, {"id": "shorttermdisability-manager", "name": "Short Term Disability - manager", "benefits": [{"key": "elimination", "name": "Elimination", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "shorttermdisability-manager"}]}, {"id": "shorttermdisability-production", "name": "Short Term Disability - production", "benefits": [{"key": "elimination", "name": "Elimination", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "shorttermdisability-production"}]}, {"id": "longtermdisability-manager", "name": "Long Term Disability - manager", "benefits": [{"key": "coverageLTD", "name": "Coverage", "values": {"Victor": "66.67%", "Current": "66.7%", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "67% first $3,500, 50% balance"}, "section": "longtermdisability-manager"}, {"key": "eliminationPeriod", "name": "Elimination Period", "values": {"Victor": "16 weeks", "Current": "119 days", "Manulife": "182 days", "Canada Life": "-", "Chambers Plan": "113 days"}, "section": "longtermdisability-manager"}]}]}, {"carriers": ["Current", "Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "longtermdisability-manager", "name": "Long Term Disability - manager", "benefits": [{"key": "benefitPeriod", "name": "Benefit Period", "values": {"Victor": "to age 65", "Current": "to age 65", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "to age 65"}, "section": "longtermdisability-manager"}, {"key": "benefitMaximumLTD", "name": "Benefit Maximum", "values": {"Victor": "$5,000", "Current": "$5,000", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "$8,000"}, "section": "longtermdisability-manager"}, {"key": "definitionOfDisability", "name": "Definition of Disability", "values": {"Victor": "2 year own occupation", "Current": "2 year own occupation", "Manulife": "any occupation", "Canada Life": "-", "Chambers Plan": "24 months own occupation"}, "section": "longtermdisability-manager"}, {"key": "taxable", "name": "Taxable", "values": {"Victor": "No", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "No"}, "section": "longtermdisability-manager"}, {"key": "quoteToNEMOrMax", "name": "Quote to NEM or Max", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "longtermdisability-manager"}, {"key": "terminationAgeLTD", "name": "Termination Age", "values": {"Victor": "Retirement or to age 65", "Current": "Retirement or to age 65", "Manulife": "Retirement or to age 65", "Canada Life": "-", "Chambers Plan": "Retirement or to age 65"}, "section": "longtermdisability-manager"}, {"key": "nonEvidenceMaximum", "name": "nonEvidenceMaximum", "values": {"Victor": "$4,500", "Current": "$3,500", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "$4,000"}, "section": "longtermdisability-manager"}]}, {"id": "longtermdisability-production", "name": "Long Term Disability - production", "benefits": [{"key": "coverageLTD", "name": "Coverage", "values": {"Victor": "66.67%", "Current": "66.7%", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "67% first $3,500, 50% balance"}, "section": "longtermdisability-production"}, {"key": "eliminationPeriod", "name": "Elimination Period", "values": {"Victor": "16 weeks", "Current": "119 days", "Manulife": "182 days", "Canada Life": "-", "Chambers Plan": "113 days"}, "section": "longtermdisability-production"}, {"key": "benefitPeriod", "name": "Benefit Period", "values": {"Victor": "to age 65", "Current": "to age 65", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "to age 65"}, "section": "longtermdisability-production"}]}]}, {"carriers": ["Current", "Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "longtermdisability-production", "name": "Long Term Disability - production", "benefits": [{"key": "benefitMaximumLTD", "name": "Benefit Maximum", "values": {"Victor": "$5,000", "Current": "$5,000", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "$8,000"}, "section": "longtermdisability-production"}, {"key": "definitionOfDisability", "name": "Definition of Disability", "values": {"Victor": "2 year own occupation", "Current": "2 year own occupation", "Manulife": "any occupation", "Canada Life": "-", "Chambers Plan": "24 months own occupation"}, "section": "longtermdisability-production"}, {"key": "taxable", "name": "Taxable", "values": {"Victor": "No", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "No"}, "section": "longtermdisability-production"}, {"key": "quoteToNEMOrMax", "name": "Quote to NEM or Max", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "longtermdisability-production"}, {"key": "terminationAgeLTD", "name": "Termination Age", "values": {"Victor": "Retirement or to age 65", "Current": "Retirement or to age 65", "Manulife": "Retirement or to age 65", "Canada Life": "-", "Chambers Plan": "Retirement or to age 65"}, "section": "longtermdisability-production"}, {"key": "nonEvidenceMaximum", "name": "nonEvidenceMaximum", "values": {"Victor": "$4,500", "Current": "$3,500", "Manulife": "$25,000", "Canada Life": "-", "Chambers Plan": "$4,000"}, "section": "longtermdisability-production"}]}, {"id": "<PERSON><PERSON>ness-manager", "name": "Critical Illness - manager", "benefits": [{"key": "amountCI", "name": "Amount", "values": {"Victor": "-", "Current": "-", "Manulife": "$30,000", "Canada Life": "-", "Chambers Plan": "-"}, "section": "<PERSON><PERSON>ness-manager"}, {"key": "coverageCI", "name": "Coverage", "values": {"Victor": "-", "Current": "-", "Manulife": "Employee Only", "Canada Life": "-", "Chambers Plan": "-"}, "section": "<PERSON><PERSON>ness-manager"}, {"key": "terminationAgeCI", "name": "Termination Age", "values": {"Victor": "-", "Current": "-", "Manulife": "Retirement or to age 70", "Canada Life": "-", "Chambers Plan": "-"}, "section": "<PERSON><PERSON>ness-manager"}]}, {"id": "criticalillness-production", "name": "Critical Illness - production", "benefits": [{"key": "amountCI", "name": "Amount", "values": {"Victor": "-", "Current": "-", "Manulife": "$30,000", "Canada Life": "-", "Chambers Plan": "-"}, "section": "criticalillness-production"}]}]}, {"carriers": ["Current", "Manulife", "Victor", "Chambers Plan"], "sections": [{"id": "criticalillness-production", "name": "Critical Illness - production", "benefits": [{"key": "coverageCI", "name": "Coverage", "values": {"Victor": "-", "Current": "-", "Manulife": "Employee Only", "Canada Life": "-", "Chambers Plan": "-"}, "section": "criticalillness-production"}, {"key": "terminationAgeCI", "name": "Termination Age", "values": {"Victor": "-", "Current": "-", "Manulife": "Retirement or to age 70", "Canada Life": "-", "Chambers Plan": "-"}, "section": "criticalillness-production"}]}, {"id": "employeeassistance-manager", "name": "Employee Assistance Program - manager", "benefits": [{"key": "coverageEA", "name": "Coverage", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes", "Canada Life": "-", "Chambers Plan": "Yes"}, "section": "employeeassistance-manager"}]}, {"id": "employeeassistance-production", "name": "Employee Assistance Program - production", "benefits": [{"key": "coverageEA", "name": "Coverage", "values": {"Victor": "Yes", "Current": "Yes", "Manulife": "Yes", "Canada Life": "-", "Chambers Plan": "Yes"}, "section": "employeeassistance-production"}]}, {"id": "healthspendingaccount-manager", "name": "Health Spending Account - manager", "benefits": [{"key": "coverageHSA", "name": "Coverage", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "healthspendingaccount-manager"}]}, {"id": "healthspendingaccount-production", "name": "Health Spending Account - production", "benefits": [{"key": "coverageHSA", "name": "Coverage", "values": {"Victor": "-", "Current": "-", "Manulife": "-", "Canada Life": "-", "Chambers Plan": "-"}, "section": "healthspendingaccount-production"}]}]}]