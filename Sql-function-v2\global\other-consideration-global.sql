
<sql splitStatements="false">
    <![CDATA[
                CREATE OR REPLACE FUNCTION sandf.fn_get_other_consideration_global(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
DECLARE
    has_rtq_only BOOLEAN := FALSE;
    result JSONB;
BEGIN
    -- Use common function to check employee class type
    has_rtq_only := sandf.fn_check_employee_class_type(plan_uuid_param);
    IF has_rtq_only THEN
        SELECT sandf.fn_get_other_consideration_RTQ(
            plan_uuid_param,
            user_id_param,
            includes_quotes_uuid
        ) INTO result;

    ELSE
        SELECT sandf.fn_get_other_consideration_multi_class(
            plan_uuid_param,
            user_id_param,
            includes_quotes_uuid
        ) INTO result;

    END IF;

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        -- Handle any errors and provide meaningful error information
        RAISE EXCEPTION 'Error in global plan design function: % (SQLSTATE: %)', SQLERRM, SQLSTATE;

END;
$$;
]]>
        </sql>
