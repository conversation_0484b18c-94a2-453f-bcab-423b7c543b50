

<sql splitStatements="false">
    <![CDATA[
CREATE OR REPLACE FUNCTION sandf.fn_get_deviations_multi_class(
    plan_uuid_param TEXT,
    user_id_param TEXT DEFAULT NULL,
    max_deviations_per_carrier INTEGER DEFAULT 4,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$

DECLARE
    quote_record RECORD;
    carrier_deviations_map JSONB := '{}'::jsonb;
    result_pages JSONB := '[]'::jsonb;
    quote_count INTEGER := 0;
    MAX_DEVIATIONS_PER_CARRIER INTEGER := max_deviations_per_carrier;
    carrier_name TEXT;
    carrier_order_map JSONB := '{}'::jsonb;
    ordered_carriers_array JSONB := '[]'::jsonb;
    carrier_item TEXT;
    carrier_order INTEGER;
    deviations_array JSONB;
    deviation_idx INTEGER;
    total_deviations INTEGER;
    current_deviation_page JSONB;

    -- Multi-class support variables
    employee_classes TEXT[];
    employee_class_count INTEGER;
    current_employee_class TEXT;
    class_suffix TEXT;

    -- Variable to track the first carrier name for display purposes
    first_carrier_name TEXT;
    display_carrier_name TEXT;

BEGIN
    -- Get all employee classes for this plan (multi-class support)
    SELECT COUNT(DISTINCT ec.name), array_agg(DISTINCT ec.name ORDER BY ec.name)
    INTO employee_class_count, employee_classes
    FROM sandf.plan p
    JOIN sandf.quote q ON q.plan_id = p.plan_id
    JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
    JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
    JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
    WHERE p.plan_uuid = plan_uuid_param::uuid
    AND ecq.formatted_quote_details IS NOT NULL
    AND ecq.formatted_quote_details != '{}'::jsonb
    AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid));

    -- Debug: Log employee classes found
    RAISE NOTICE 'Found % employee classes: %', employee_class_count, employee_classes;

    -- Build carrier order map first (before processing deviations)
    FOR quote_record IN
        SELECT ecq.formatted_quote_details,
               c.description as carrier_description,
               q.quote_id,
               q.quote_uuid,
               ec.name as employee_class_name
        FROM sandf.plan p
        JOIN sandf.quote q ON q.plan_id = p.plan_id
        JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
        JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
        JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
        WHERE p.plan_uuid = plan_uuid_param::uuid
        AND ec.name = ANY(employee_classes)
        AND ecq.formatted_quote_details IS NOT NULL
        AND ecq.formatted_quote_details != '{}'::jsonb
        AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
    LOOP
        carrier_name := quote_record.carrier_description;

        -- Get carrier order using existing function
        carrier_order := sandf.get_user_preference_order(
            user_id_param,
            plan_uuid_param,
            quote_record.quote_id,
            quote_record.quote_uuid,
            999999
        );

        -- Build carrier order map
        IF NOT carrier_order_map ? carrier_name THEN
            carrier_order_map := carrier_order_map || jsonb_build_object(
                carrier_name,
                jsonb_build_object(
                    'order', carrier_order,
                    'quote_id', quote_record.quote_id,
                    'quote_uuid', quote_record.quote_uuid
                )
            );
        END IF;
    END LOOP;

    -- Debug: Log carrier order map
    RAISE NOTICE 'Carrier order map: %', carrier_order_map;

    -- Set the first carrier name for display purposes
    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
        LIMIT 1
    LOOP
        first_carrier_name := carrier_item;
        RAISE NOTICE 'First carrier name set to: %', first_carrier_name;
    END LOOP;

    -- Process deviations for each employee class
    FOREACH current_employee_class IN ARRAY employee_classes
    LOOP
        class_suffix := current_employee_class;

        -- Collect deviations from all carriers for this employee class
        FOR quote_record IN
            SELECT ecq.formatted_quote_details,
                   c.description as carrier_description,
                   q.quote_id,
                   q.quote_uuid
            FROM sandf.plan p
            JOIN sandf.quote q ON q.plan_id = p.plan_id
            JOIN sandf.carrier c ON c.carrier_id = q.carrier_id
            JOIN sandf.employee_class_quote ecq ON q.quote_id = ecq.quote_id
            JOIN sandf.employee_class ec ON ecq.employee_class_id = ec.employee_class_id
            WHERE p.plan_uuid = plan_uuid_param::uuid
            AND ec.name = current_employee_class
            AND ecq.formatted_quote_details IS NOT NULL
            AND ecq.formatted_quote_details != '{}'::jsonb
            AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
            ORDER BY
                COALESCE((carrier_order_map -> c.description ->> 'order')::integer, 999999) ASC,
                c.description ASC
        LOOP
            carrier_name := quote_record.carrier_description;

            -- Extract deviations for this carrier and employee class
            deviations_array := quote_record.formatted_quote_details -> 'deviations';

            -- Debug: Log what we're processing
            RAISE NOTICE 'Processing carrier: %, class: %, deviations found: %',
                carrier_name, class_suffix,
                CASE WHEN deviations_array IS NOT NULL THEN jsonb_array_length(deviations_array) ELSE 0 END;

            IF deviations_array IS NOT NULL AND jsonb_typeof(deviations_array) = 'array' THEN
                -- Store deviations with carrier-class combination as key
                carrier_deviations_map := carrier_deviations_map || jsonb_build_object(
                    carrier_name || '-' || class_suffix, deviations_array
                );

                -- Debug: Log the key being stored
                RAISE NOTICE 'Stored deviations with key: %', carrier_name || '-' || class_suffix;
            ELSE
                RAISE NOTICE 'No deviations found for carrier: %, class: %', carrier_name, class_suffix;
            END IF;
        END LOOP;
    END LOOP;

    -- Debug: Log final carrier deviations map
    RAISE NOTICE 'Final carrier_deviations_map: %', carrier_deviations_map;

    -- Build ordered carriers array with class suffixes for multi-class support
    FOR carrier_item IN
        SELECT key as carrier_name
        FROM jsonb_each(carrier_order_map)
        ORDER BY (value ->> 'order')::integer ASC, key ASC
    LOOP
        -- For each carrier, add entries for each employee class
        FOREACH current_employee_class IN ARRAY employee_classes
        LOOP
            -- Use "Current" for the first carrier, otherwise use actual name
            IF carrier_item = first_carrier_name THEN
                display_carrier_name := 'Current-' || current_employee_class;
            ELSE
                display_carrier_name := carrier_item || '-' || current_employee_class;
            END IF;

            ordered_carriers_array := ordered_carriers_array || jsonb_build_array(display_carrier_name);
        END LOOP;
    END LOOP;

    -- Create flat structure with 4 deviations per page
    DECLARE
        carrier_names_array TEXT[];
        current_carrier_name TEXT;
        all_deviations JSONB := '[]'::jsonb;
        current_page_data JSONB;
        deviations_per_page INTEGER := 6;
        total_deviations_count INTEGER;
        page_start_idx INTEGER;
        page_end_idx INTEGER;
        deviation_obj JSONB;
    BEGIN
        -- Convert ordered carriers to array for easier indexing
        SELECT array_agg(value::text ORDER BY ordinality)
        INTO carrier_names_array
        FROM jsonb_array_elements_text(ordered_carriers_array) WITH ORDINALITY;

        -- Create flat array of all deviations with carrier-class names
        FOR carrier_idx IN 1..COALESCE(array_length(carrier_names_array, 1), 0) LOOP
            current_carrier_name := carrier_names_array[carrier_idx];

            -- Extract the original carrier name and class from the display name
            -- Display names are in format "Current-ClassName" or "CarrierName-ClassName"
            DECLARE
                original_carrier_name TEXT;
                carrier_class_key TEXT;
                last_dash_position INTEGER;
            BEGIN
                -- Find the position of the last dash to separate carrier from class
                last_dash_position := length(current_carrier_name) - position('-' in reverse(current_carrier_name)) + 1;

                IF last_dash_position > 0 AND last_dash_position < length(current_carrier_name) THEN
                    -- Extract class suffix from after the last dash
                    class_suffix := substring(current_carrier_name from last_dash_position + 1);
                    -- Extract carrier name (everything before the last dash)
                    IF starts_with(current_carrier_name, 'Current-') THEN
                        original_carrier_name := first_carrier_name;
                    ELSE
                        original_carrier_name := substring(current_carrier_name from 1 for last_dash_position - 1);
                    END IF;

                    -- Build the key for carrier_deviations_map
                    carrier_class_key := original_carrier_name || '-' || class_suffix;
                ELSE
                    -- Fallback if no dash found
                    original_carrier_name := current_carrier_name;
                    carrier_class_key := current_carrier_name;
                END IF;

                deviations_array := COALESCE(carrier_deviations_map -> carrier_class_key, '[]'::jsonb);

                -- Debug: Log deviation processing
                RAISE NOTICE 'Processing deviations for display name: %, key: %, found % deviations',
                    current_carrier_name, carrier_class_key, jsonb_array_length(deviations_array);

                -- Add each deviation as a separate object
                FOR deviation_idx IN 0..COALESCE(jsonb_array_length(deviations_array), 0) - 1 LOOP
                    deviation_obj := jsonb_build_object(
                        'name', current_carrier_name,  -- Use display name with class suffix
                        'deviations', deviations_array -> deviation_idx
                    );
                    all_deviations := all_deviations || jsonb_build_array(deviation_obj);

                    -- Debug: Log each deviation added
                    RAISE NOTICE 'Added deviation: %', deviations_array -> deviation_idx;
                END LOOP;
            END;
        END LOOP;

        -- Create pages with exactly 4 deviations each
        total_deviations_count := jsonb_array_length(all_deviations);

        -- Create pages with 4 deviations each
        FOR page_start_idx IN 0..total_deviations_count-1 BY deviations_per_page LOOP
            page_end_idx := LEAST(page_start_idx + deviations_per_page - 1, total_deviations_count - 1);
            current_page_data := '[]'::jsonb;

            -- Add deviations to current page
            FOR deviation_idx IN page_start_idx..page_end_idx LOOP
                current_page_data := current_page_data || jsonb_build_array(
                    all_deviations -> deviation_idx
                );
            END LOOP;

            -- Add page to results
            result_pages := result_pages || jsonb_build_array(
                jsonb_build_object(
                    'carrierData', current_page_data
                )
            );
        END LOOP;
    END;

    -- Return array of paginated results
    RETURN result_pages;
END;
$$;
]]>
        </sql>