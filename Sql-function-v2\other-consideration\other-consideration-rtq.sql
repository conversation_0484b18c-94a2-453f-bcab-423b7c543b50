  <sql splitStatements="false">
            <![CDATA[
        CREATE OR REPLACE FUNCTION sandf.fn_get_other_consideration_RTQ(
    target_plan_uuid TEXT,
    user_id TEXT DEFAULT NULL,
    includes_quotes_uuid Text[] DEFAULT NULL
)
RETURNS JSONB
LANGUAGE plpgsql
AS $$
            DECLARE
            v_plan_id INTEGER;
            v_employee_class_id INTEGER;
            quote_record RECORD;
            quote_json JSONB;
            carrier_name TEXT;
            carrier_list TEXT[] := '{}';
            benefit_keys TEXT[] := ARRAY[
            'insuredBenefitsRateGuarantee',
            'eHCAndDentalRateGuarantee',
            'renewalRateCap',
            'renewalModel'
            ];
            benefit_names TEXT[] := ARRAY[
            'Insured Benefits',
            'EHC + Dental',
            'Renewal Rate Cap',
            'Renewal Model'
            ];
            benefit_index INT;
            benefit_key TEXT;
            benefit_name TEXT;
            benefit_value TEXT;
            benefit_value_map JSONB := '{}';
            rate_guarantees JSONB;
            values_json JSONB;
            section_benefits JSONB := '[]'::JSONB;
            section_json JSONB;

            -- Variable to track the first carrier name for display purposes
            first_carrier_name TEXT;
            display_carrier_name TEXT;
            BEGIN
            -- Get plan_id
            SELECT p.plan_id INTO v_plan_id
            FROM sandf.plan p
            WHERE p.plan_uuid = target_plan_uuid::uuid
            ORDER BY p.plan_id ASC
                LIMIT 1;

            IF v_plan_id IS NULL THEN
            RETURN jsonb_build_object(
            'RatingFactorsAndGuarantees', jsonb_build_object(
            'carriers', carrier_list,
            'sections', '[]'::JSONB
            )
            );
            END IF;

            -- Get first RTQ employee_class_id
            SELECT employee_class_id INTO v_employee_class_id
            FROM sandf.employee_class
            WHERE plan_id = v_plan_id 
            ORDER BY employee_class_id ASC
                LIMIT 1;

            IF v_employee_class_id IS NULL THEN
            RETURN jsonb_build_object(
            'RatingFactorsAndGuarantees', jsonb_build_object(
            'carriers', carrier_list,
            'sections', '[]'::JSONB
            )
            );
            END IF;

            -- Get quotes ordered by user preference
            FOR quote_record IN
            SELECT ecq.*, q.quote_id, q.quote_uuid, c.description as carrier_name
            FROM sandf.employee_class_quote ecq
            JOIN sandf.quote q ON ecq.quote_id = q.quote_id
            JOIN sandf.carrier c ON q.carrier_id = c.carrier_id
            WHERE ecq.employee_class_id = v_employee_class_id
            AND (includes_quotes_uuid IS NULL OR q.quote_uuid::text = ANY(includes_quotes_uuid))
            ORDER BY
                CASE WHEN user_id IS NOT NULL THEN
                    sandf.get_user_preference_order(
                        user_id,
                        target_plan_uuid::TEXT,
                        q.quote_id,
                        q.quote_uuid,
                        999999
                    )
                ELSE
                    q.quote_id
                END ASC,
                c.description ASC
            LOOP
            quote_json := quote_record.formatted_quote_details::jsonb;
             carrier_name := COALESCE(quote_record.carrier_name, 'Unknown Carrier');

            -- Capture the first carrier name for display purposes
            IF first_carrier_name IS NULL THEN
                first_carrier_name := carrier_name;
            END IF;

            -- Use "Current" for the first carrier, otherwise use actual name
            IF carrier_name = first_carrier_name THEN
                display_carrier_name := 'Current';
            ELSE
                display_carrier_name := carrier_name;
            END IF;

            IF NOT display_carrier_name = ANY(carrier_list) THEN
            carrier_list := array_append(carrier_list, display_carrier_name);
            END IF;

            rate_guarantees := quote_json #> '{benefitPremiums,rateGuarantees}';

            -- Loop through benefit keys and assign values per carrier
            FOR benefit_index IN 1 .. array_length(benefit_keys, 1)
            LOOP
            benefit_key := benefit_keys[benefit_index];
            benefit_name := benefit_names[benefit_index];
            benefit_value := CASE
                WHEN rate_guarantees IS NULL THEN '-'
                WHEN rate_guarantees ->> benefit_key IS NULL THEN '-'
                WHEN rate_guarantees ->> benefit_key = '' THEN '-'
                WHEN rate_guarantees ->> benefit_key = 'null' THEN '-'
                ELSE rate_guarantees ->> benefit_key
            END;

             IF benefit_value IS NOT NULL AND benefit_value != '-' THEN
                BEGIN
                    PERFORM sandf.safe_parse_numeric(benefit_value);
                EXCEPTION WHEN OTHERS THEN
                    benefit_value := '-';
                END;
            END IF;

            -- Merge value for current carrier into the JSON map
            IF benefit_value_map ? benefit_key THEN
            benefit_value_map := jsonb_set(
            benefit_value_map,
            ARRAY[benefit_key, display_carrier_name],
            to_jsonb(benefit_value),
            true
            );
            ELSE
            benefit_value_map := jsonb_set(
            benefit_value_map,
            ARRAY[benefit_key],
            jsonb_build_object(display_carrier_name, benefit_value),
            true
            );
            END IF;
            END LOOP;
            END LOOP;

            -- Build benefits array
            FOR benefit_index IN 1 .. array_length(benefit_keys, 1)
            LOOP
            benefit_key := benefit_keys[benefit_index];
            benefit_name := benefit_names[benefit_index];
            values_json := benefit_value_map -> benefit_key;

            section_benefits := section_benefits || jsonb_build_object(
            'key', benefit_key,
            'name', benefit_name,
            'values', values_json
            );
            END LOOP;

            -- Final section structure
            section_json := jsonb_build_object(
            'key', 'rateGuarantees',
            'name', 'Rate Guarantees',
            'benefits', section_benefits
            );

            RETURN jsonb_build_object(
                    'RatingFactorsAndGuarantees', jsonb_build_object(
                            'carriers', carrier_list,
                            'sections', jsonb_build_array(section_json)
                                                  )
                   );
            END;
            $$;

        ]]>
        </sql>