{"queries": {"rateSheet": {"queryKey": "rateSheet1", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "excludesArray": [], "includesArray": [], "includesQuotes": []}}, "deviations": {"queryKey": "deviations", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "includesQuotes": []}}, "planDesign": {"queryKey": "planDesign", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "excludesArray": [], "includesArray": [], "includesQuotes": []}}, "ratesPerEE": {"queryKey": "ratesPerEE", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "excludesArray": [], "includesArray": [], "includesQuotes": [], "numberOfEmployees": null}}, "customerData": {"queryKey": "customerData", "queryType": "SQL", "parameters": {"planUuid": null}}, "cuttingChase": {"queryKey": "cuttingChase", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "includesQuotes": []}}, "planOverview": {"queryKey": "planOverview", "queryType": "SQL", "parameters": {"includesArray": []}}, "renewalCharges": {"queryKey": "renewalCharges", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "includesQuotes": []}}, "targetedClaims": {"queryKey": "targetedClaims", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "includesQuotes": []}}, "otherConsiderations": {"queryKey": "otherConsiderations", "queryType": "SQL", "parameters": {"planUuid": null, "userUuid": null, "includesQuotes": []}}}}