-- Test script for the updated plan design function with config-based filtering

-- Test the function with a sample plan UUID
SELECT sandf.fn_get_plan_design_report_multi_class(
    'your-plan-uuid-here'::TEXT,
    NULL::TEXT,
    NULL::TEXT[],
    NULL::TEXT[]
);

-- Test with specific includes (only process certain sections)
SELECT sandf.fn_get_plan_design_report_multi_class(
    'your-plan-uuid-here'::TEXT,
    NULL::TEXT,
    ARRAY['lifeInsuranceADAD', 'extendedHealth', 'dental']::TEXT[],
    NULL::TEXT[]
);

-- Test with excludes (exclude certain sections)
SELECT sandf.fn_get_plan_design_report_multi_class(
    'your-plan-uuid-here'::TEXT,
    NULL::TEXT,
    NULL::TEXT[],
    ARRAY['shortTermDisability', 'healthSpendingAccount']::TEXT[]
);
