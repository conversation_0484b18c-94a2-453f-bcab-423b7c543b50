# Plan Design Multi-Class Function Updates

## Overview
Updated the `fn_get_plan_design_report_multi_class` function to use configuration-based data filtering and ordering based on the `config.json` planDetails structure.

## Key Changes Made

### 1. Configuration-Based Filtering
- **Before**: Hardcoded section names and display names
- **After**: Uses `config.json` planDetails structure dynamically
- Only processes sections that exist in the configuration
- Uses `displayItems` from config for field mapping and display names
- Filters out fields not mentioned in the configuration

### 2. Explicit Section Ordering
- **Before**: Hardcoded section order using CASE statements
- **After**: Uses explicit `order` numbers from `config_plan_details`
- Each section now has an `order` property (1-9) for precise sorting
- Guarantees consistent ordering regardless of JSON key sequence
- Sections appear in the exact order: lifeInsurance(1), ADAD(2), dependentLife(3), longTermDisability(4), shortTermDisability(5), criticalIllness(6), employeeAssistance(7), extendedHealth(8), dental(9)

### 3. Carrier Data Filtering
- **Before**: Showed all carriers even if they had no data
- **After**: Hides entire carriers when they have no actual data (only "-" values)
- Only includes carriers that have at least one non-empty, non-"-" value
- Removes carriers with no data from all benefit objects

### 4. Fixed Pagination Logic
- **Before**: Could break sections across pages
- **After**: Never breaks section objects - complete sections only
- If a section doesn't fit on current page, it's moved to a new page entirely
- Maintains section integrity across pagination

### 5. lifeInsuranceADAD Handling
- Properly splits `lifeInsuranceADAD` into separate `lifeInsurance` and `ADAD` sections
- Each section uses its own displayItems configuration
- Maintains proper section naming and field mapping

## Configuration Structure Used

The function now uses the exact structure from `config.json`:

```json
{
  "planDetails": {
    "lifeInsurance": {
      "order": 1,
      "lifeInsurance": "Life Insurance",
      "displayItems": {
        "maximumLife": "Maximum LI",
        "ageReduction": "Age Reduction",
        "coverageLife": "Coverage LI",
        "terminationAgeLife": "Termination Age LI"
      }
    },
    "ADAD": {
      "order": 2,
      "ADAD": "Accidental Death & Dismembermenet",
      "displayItems": {
        "maximumADAD": "Maximum AD&D",
        "terminationAgeADAD": "Termination Age ADAD"
      }
    }
    // ... other sections with order: 3, 4, 5, 6, 7, 8, 9
  }
}
```

## Benefits

1. **Maintainable**: Changes to section names or field mappings only require config.json updates
2. **Flexible**: Easy to add/remove sections by modifying configuration
3. **Clean Output**: Only shows relevant data as defined in configuration
4. **Proper Pagination**: Sections are never broken across pages
5. **Data-Driven**: Carriers without data are automatically filtered out

## Usage

The function maintains the same interface:
```sql
SELECT sandf.fn_get_plan_design_report_multi_class(
    plan_uuid_param,
    user_id_param,
    includes_param,
    excludes_param
);
```

The output structure follows the new-output.json format with proper section separation and configuration-based ordering.
